﻿Pyarmor 8.0 最终用户许可协议

重要！请仔细阅读本协议。下载、安装或使用整个软件或其任何部分，即表示您接受本协议
的所有条款和条件。您同意本协议与任何书面协议一样具有法律效力。

如果您不同意所有这些条款和条件，请不要使用或访问该软件。如果您已经支付了使用该软
件的许可费，但不同意这些条款，您可以退还该软件以获得全额退款，但前提是您 (A) 未
使用该软件，并且 (B) 在初次购买该软件的三十 (30) 天内退还该软件。

如果您同意这些条款和条件，即表示您确认自己已年满或超过 18 岁并且具有足够的民事行
为能力签订在法律上有约束力的协议。

以下协议在 Pyarmor 开发者 赵俊德（下称“许可人”）和任何安装、访问或以其它方式使用
本软件的自然人或者团体机构（下称“用户”）之间订立。

1. 概念和定义

本软件
下文中特指 Pyarmor 8.0 之后的版本，不包含 Pyarmor 8.0 之前的版本。虽然为了兼
容性，Pyarmor 8.0 中包含老版本中功能，但是本协议中的许可协议并不适用于部分功能。

产品
下文中特指的是部分或者全部使用 Python 脚本实现功能的软件，该软件同时还通过销售进
行盈利，除非特别说明，下文中的产品不包含非盈利的任何软件。

一种产品
一种产品在本协议中指的是独立销售的一个软件的所有组成部分，包括开发需要的各种设备，
以及提供支持的服务器，云服务器等。一种产品也包括产品的当前版本，历史版本，以及将
来的升级版本。一种产品也包括基础功能相同，组合不同特殊功能而形成的不同版本的产品，
这种产品的特征是不同版本对外销售名称一样，只是通过辅助名称等来进行区分。

用户
使用本软件的个人或者组织机构。

客户
在本文中特指的是使用用户产品的个人或者组织机构。

用户产品
用户拥有完全或者部分产权的产品。

其它产品
产权不属于用户的产品。

脚本
下文中特指的 Python 脚本文件。

加密脚本
下文中指的是使用本软件加密之后输出的脚本。

用户脚本
用户拥有完全或者部分产权的脚本。

其它脚本
用户不具备任何产权的脚本

许可证
下文中指的是由许可人生成并发送给用户的特殊格式的文件，本软件会根据该文件来决定某
些功能是否可用

激活许可证
指的是用户第一次使用许可证运行本软件，称之为激活许可证。激活之后的状态称之为许可
证已经激活；如果许可证从来没有被用来运行本软件，那么称之为许可证没有被激活。

停用许可证
指的是许可人在认证服务器对某一个许可证取消授权，取消授权之后的许可证等同于没有许
可证，该许可证授予的相关功能无法在继续使用。

1.1 本软件的功能定义

基本加密功能
是指没有使用任何选项的加密功能。

JIT 保护
是指使用动态代码生成机制对加密脚本进行保护的功能。

Themedia 保护
是指使用第三方工具 Themedia 对 Widnows 动态库进行保护的功能。

Assert 保护
是指保护加密脚本不会被替换或者非法注入的保护功能。

设置脚本有效期
是指能够限制加密脚本运行有效期的功能。

绑定加密脚本到设备
是指能够限制加密脚本运行在指定设备的功能。

混淆字符串功能
是指对脚本中的字符串常量进行混淆保护的功能。

RFT 加密模式
是指通过重命名脚本中的函数，类，方法和变量的名称来保护脚本的功能。

BCC 加密模式
是指把 Python 脚本中部分函数转换成为对应的 C 函数，通过编译直接生成机器指令代码，
从而对脚本进行保护的功能。

2. 本软件的许可模式

2.1 试用版本

下载和安装本软件表示自动接受试用许可协议，试用版本有如下的限制

(1) 加密功能对脚本大小有限制，不能加密超过限制的大脚本。
(2) 混淆字符串功能在试用版中无法使用。
(3) RFT 加密模式，BCC 加密模式在试用版无法使用。
(4) 不可以应用于加密商用产品。特别的，如果商用产品的累计销售额小于基础版许
    可证费用乘以 100，可以暂时使用；但是一旦累计销售额超过阀值，就不可以在
    继续使用。
(5) 运行辅助包的名称 "pyarmor_runtime_000000" 不可以被设置和修改
(6) 不可以使用本软件提供任何形式的加密服务，不管是通过应用程序还是网络服务。
    总之在任何情况下都不允许使用本软件加密其他人的脚本。
(7) 不支持 obf-code 大于 1 的任何加密模式

试用版本中功能限制，需要通过许可授权来解锁相关功能。

2.2 许可授权

许可授权需要通过购买相应的许可证来获取，购买许可证可以通过指定的网站购买。

每一个许可证都有一个 18 位字符长度唯一的编号，并授权给有且只有一种产品使用。也就
是说，任何一种使用本软件进行保护的产品都有自己唯一的许可证编号，不允许两种不同产
品使用相同的许可证编号。

本软件提供三种许可证，分别解锁不同的功能

2.2.1 基础版许可证

基础版许可证解锁限制 2.1 中的 (1) (2) (4) (5) (7).

基础版许可证加密脚本的时候需要在线验证许可证

2.2.2 专家版许可证

专家版许可证解锁限制 2.1 中的 (1) (2) (3) (4) (5) (7).

专家版许可证加密脚本的时候需要在线验证许可证

2.2.3 集团版许可证

集团版许可证解锁限制 2.1 中的 (1) (2) (3) (4) (5) (7).

集团版许可证加密脚本的时候不需要在线验证许可证

不管哪一种许可证，运行加密脚本的时候都无需验证许可证，本软件对于加密脚本的运行没
有任何控制和限制。

2.3 购买和退款

除了购买软件许可的费用之外，没有其它任何费用。获得许可的用户可以使用本软件在许可
的范围之内加密任何脚本并自由发布，不需要在向许可人支付任何费用。

购买软件许可的费用是一次性收费，可以永久在购买本软件时候的版本中使用，但是许可证
可能在任何一个升级版本中失效，许可人不承诺许可证可以在今后所有的升级版本中使用。

一旦激活许可证之后，不在支持退款，购买之后没有激活许可证，在三十天之内支持取消许
可证并退款。但是如果购买时间超过三十天，没有激活的许可证也不再支持退款。

2.4 合理使用原则

如果用户有多个产品并且已经为第一个产品购买许可证，其他产品满足下列条件可以使用第
一个产品的许可证：

(1) 如果该产品的销售收入在当前许可证费用的 100 倍之内，那么该产品可以使用第一个
    产品的许可证。

(2) 如果该产品的销售输入超过当前许可证费用的 100 倍，那么该产品不可以继续使用
    第一个产品的许可证，需要单独购买新的许可证。

本许可证并不旨在限制用户根据适用条款享有的任何权利、公平交易或其他等效的合理使用。

3. 许可人的承诺

3.1 对于在开发设备运行本软件进行加密

本软件没有任何后门以及和加密无关的功能代码，本软件在加密过程中不会记录和访问无关
的设备数据和信息，下面列出的资源除外：

(1) 基础版和专家版许可模式下使用本软件需要通过互联网进行许可认证
(2) 本软件会访问设备名称，硬盘序列号，以太网卡地址，IPv4/IPv6 地址，并且仅用于
    许可认证

3.2 对于在客户设备运行的加密脚本

(1) 本软件对于加密脚本的运行没有任何控制和限制，加密脚本的运行限制完全由用户控制
(2) 除非加密脚本设置了网络时间的有效期，否则本软件不会访问互联网
(3) 除非加密脚本设置了绑定到设备，否则本软件不会访问任何设备和系统信息
(4) 许可证编号和被授权的产品名称会嵌入到加密脚本中，除此之外，加密脚本中没有任何用
    户相关的注册信息，例如注册名称和邮箱等。

4. 使用本软件的限制和约束

4.1 本软件只能用于加密用户脚本，不能以任何方式加密其它脚本，包含但不限于如下方式是
    许可禁止的行为

    (1) 在用户发布的产品中包含本软件自身去加密其他人的脚本
    (2) 在服务器上运行本软件，通过网络向其它人提供基于本软件的加密脚本服务
    (3) 接受其它人的请求，在自己的设备上加密其他人的脚本

4.2 用户可以修改本软件相关的 Python 脚本以满足使用方面的额外需求，但是修改后的脚
    本只能在许可证范围内使用，不得分发给其它人

4.3 同一个许可下面，使用本软件的设备数目有限制。这里的设备是指安装本软件并使用本
    软件对脚本进行加密的设备，不是指运行加密脚本的客户机器。

4.4 每一个许可证绑定到一种产品，不可以转移给其它产品。

4.5 用户注册信箱被盗用视同用户自己使用。

4.6 本软件只能安装在用户的开发环境，只能用于加密客户的脚本，不能安装在客户设备或者
    生产环境去加密客户的脚本

5. 导致许可证被停用的行为

5.1 使用 Paypal，信用卡等网络支付的用户，一旦在激活许可证之后进行撤单，该许可证
    会被永久停用。即便撤单行为失败或者用户主动取消撤单，该许可证也无法在重新使用。

5.2 用户丢失许可，许可人在发现使用设备数量显著超过许可之后，可以在不通知用户的情
    况下停用该许可证。包含但不限于下列方式

    (1) 用户主动把许可文件提供给其他用户
    (2) 用户保管不妥造成许可文件无意被其它人获取
    (3) 用户数据被黑客窃取

5.3 当许可人发现许可证的注册产品和实际的用户产品不一致的时候，可以在不通知用户的
    情况下停用该许可证。

    但是在下列情况下，即便实际的产品名称和许可证绑定的产品名称不一致，该许可证可
    以继续合法使用，不会被停用：

    (1) 公司被收购或者更改名称，只要产品名称不发生变化，无需修改注册名称，可以继
        续合法使用在原来的产品

    (2) 产品名称发生变化，只要产品能很明显的证明和原来的产品是同一个，可以继续合
        法使用许可证在修改名称后的产品。如果不能很明显的证明，需要购买新的许可证。

    (3) 对于开发早期产品名称没有确定的情况，本软件允许在第一次许可登记的时候使用
        三个英文字符 “TBD“ 来指定产品名称，允许在其后修改为真正的产品名称。对于产
        品名称为 “TBD“ 的许可证，一旦开始销售，必须修改为真正的产品名称，否则也会
        被认为是非授权使用。

6. 许可限制

用户不得（且不得允许任何第三方）：

6.1 以任何方式对本软件的二进制文件进行反编译、反汇编或逆向工程；

6.2 分发、出售、转让许可、出租、租赁该软件（或其任何部分），也不得将其用于分时、
    托管、服务提供商等目的；

6.3 去除软件中包含的任何产品标识、专有权、版权或其他通知；

6.4 复制、修改（本协议中明确允许的除外），创建软件任何部分的衍生作品，或者将该软
    件并入其他软件或与之合并，有许可人以书面形式明确授权的除外；

6.5 尝试规避或禁用防止擅自使用该软件的安全密钥机制（除非且仅当适用的法律禁止或限
    制此类限制）。

7. 担保免责声明

本软件和所有服务均“按原样”提供。许可人不做任何其他保证、条件或承诺（无论是明示还
是默示的，是法定还是其他形式的），包括但不限于有关所有权、适销性、针对特定目的的
适用性或不侵权的保证。许可人不提供以下保证：

(1) 该软件符合您的要求；
(2) 该软件不含错误或缺陷；
(3) 该软件的安全性、可靠性、及时性或性能达到特定水平；
(4) 该软件中的任何错误都将得到纠正；
(5) 该软件可产生特定结果或输出。

8. 对高风险活动的免责声明

本软件不具有容错性，其设计、制造或预定用途并非为了生命援助、医疗、急救、关键任务
或其他严格责任或危险活动（以下简称“高风险活动”）。许可人特此否认对高风险活动的适
用性的任何明示或默示的保证。

9. 遵守法律

在使用该软件以及从软件产生的任何结果时，用户有责任遵守所有适用的法律、法规和业务
守则。

10. 协议的修订和解释

本协议由许可人负责解释。任何时候许可人对本协议做出任何修改，修改版本自动适用于用
户。
