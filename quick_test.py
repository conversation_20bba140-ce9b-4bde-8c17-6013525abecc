#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test script for Sakani reservation
Usage: python quick_test.py <auth_token> <national_id> <unit_code> <project_id>
"""

import sys
import os
import json

# Add paths for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "17-9-2025"))

def import_sakani_classes():
    """Try to import Sakani classes from different possible locations"""
    try:
        from mainclass_بحث import Sakani, UnitObject
        return Sakani, UnitObject
    except ImportError:
        try:
            # Try with different encoding or path
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "mainclass", 
                os.path.join("17-9-2025", "mainclass بحث.py")
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module.Sakani, module.UnitObject
        except Exception as e:
            print(f"❌ Could not import Sakani classes: {e}")
            print("Make sure the mainclass بحث.py file is in the 17-9-2025 directory")
            sys.exit(1)

def create_test_unit(unit_code: str, project_id: int, UnitObject):
    """Create a test unit object"""
    mock_response = {
        'id': f'test_unit_{unit_code}',
        'attributes': {
            'unit_code': unit_code,
            'land_number': 'test_land',
            'land_type': 'residential',
            'model': 'test_model',
            'unit_size': '500',
            'unit_type': 'villa'
        }
    }
    
    unit = UnitObject(mock_response)
    unit.setProjectID(project_id)
    unit.setProjectCode(f"PC_{project_id}")
    return unit

def test_step_by_step(auth_token: str, national_id: str, unit_code: str, project_id: int):
    """Test each step of the reservation process"""
    
    print("🔧 Importing Sakani classes...")
    Sakani, UnitObject = import_sakani_classes()
    
    print("🔧 Initializing Sakani instance...")
    sakani = Sakani(
        auth=auth_token,
        NationalID=national_id,
        token="test",
        chat_id="test",
        token2="test",
        chat_id2="test",
        timeout=15
    )
    
    print(f"📦 Creating test unit for code: {unit_code}")
    test_unit = create_test_unit(unit_code, project_id, UnitObject)
    
    # Step 1: Captcha Validation
    print("\n" + "="*50)
    print("🔐 STEP 1: Testing Captcha Validation")
    print("="*50)
    
    try:
        recaptcha_token = sakani.validate_captcha("start_booking", unit_code, project_id)
        if recaptcha_token:
            print(f"✅ Captcha validation SUCCESS")
            print(f"🎫 Token: {recaptcha_token[:50]}...")
        else:
            print("❌ Captcha validation FAILED")
            return False
    except Exception as e:
        print(f"❌ Captcha validation ERROR: {e}")
        return False
    
    # Step 2: Booking Precondition
    print("\n" + "="*50)
    print("📋 STEP 2: Testing Booking Precondition")
    print("="*50)
    
    try:
        precondition_response = sakani.start_booking_precondition(project_id)
        if precondition_response:
            print("✅ Booking precondition SUCCESS")
            print(f"📄 Response: {json.dumps(precondition_response, indent=2)}")
            request_id = precondition_response.get('data', {}).get('request_id')
            if not request_id:
                print("❌ No request_id in response")
                return False
        else:
            print("❌ Booking precondition FAILED")
            return False
    except Exception as e:
        print(f"❌ Booking precondition ERROR: {e}")
        return False
    
    # Step 3: Precondition Status Check
    print("\n" + "="*50)
    print("🔍 STEP 3: Testing Precondition Status")
    print("="*50)
    
    try:
        status_response = sakani.check_booking_precondition_status(request_id, project_id)
        if status_response:
            print("✅ Precondition status SUCCESS")
            print(f"📄 Response: {json.dumps(status_response, indent=2)}")
            
            # Check blocking reasons
            block_reasons = status_response.get('data', {}).get('block_booking_reason', [])
            if block_reasons:
                print(f"⚠️  BLOCKED: {block_reasons}")
                print("Cannot proceed with reservation due to blocking reasons")
                return False
            
            # Get booking token
            booking_session = status_response.get('data', {}).get('booking_session', {})
            booking_token = booking_session.get('data', {}).get('attributes', {}).get('booking_token')
            if booking_token:
                print(f"🎫 Booking token: {booking_token[:50]}...")
            else:
                print("⚠️  No booking token found")
        else:
            print("❌ Precondition status FAILED")
            return False
    except Exception as e:
        print(f"❌ Precondition status ERROR: {e}")
        return False
    
    # Step 4: Unit Reservation
    print("\n" + "="*50)
    print("🏠 STEP 4: Testing Unit Reservation")
    print("="*50)
    
    try:
        reserve_payload = {
            "data": {
                "attributes": {
                    "unit_code": unit_code
                }
            },
            "recaptcha_auth_token": recaptcha_token
        }
        
        print(f"📤 Sending reservation request...")
        response = sakani._make_request(
            "POST",
            "https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
            json_payload=reserve_payload,
            extra_headers=sakani.headers
        )
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Unit reservation SUCCESS")
            print(f"📄 Response: {json.dumps(response_data, indent=2)}")
        else:
            print(f"❌ Unit reservation FAILED")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Unit reservation ERROR: {e}")
        return False
    
    print("\n" + "="*50)
    print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    print("="*50)
    return True

def main():
    if len(sys.argv) != 5:
        print("Usage: python quick_test.py <auth_token> <national_id> <unit_code> <project_id>")
        print("\nExample:")
        print("python quick_test.py 'eyJ0eXAiOiJKV1Q...' '1234567890' '7C840FCB9CCE5768...' '1493'")
        sys.exit(1)
    
    auth_token = sys.argv[1]
    national_id = sys.argv[2]
    unit_code = sys.argv[3]
    
    try:
        project_id = int(sys.argv[4])
    except ValueError:
        print("❌ Project ID must be a number")
        sys.exit(1)
    
    print("🧪 SAKANI RESERVATION QUICK TEST")
    print("="*50)
    print(f"Auth Token: {auth_token[:20]}...")
    print(f"National ID: {national_id}")
    print(f"Unit Code: {unit_code}")
    print(f"Project ID: {project_id}")
    print("="*50)
    
    success = test_step_by_step(auth_token, national_id, unit_code, project_id)
    
    if success:
        print("\n✅ Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
