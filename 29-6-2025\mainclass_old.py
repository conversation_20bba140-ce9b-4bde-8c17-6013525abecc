# استدعاء المكتبات الاساسية
import requests , typing
from Packages import (
    pyqtSignal ,
    QObject , 
    QTime
)
import random as rm
import configparser
import os , datetime , time
from bs4 import BeautifulSoup


class TimeObject(QObject): # "2025-03-12T15:42:52.323Z"
    timeoutSignal = pyqtSignal()

    def translateLoginTime(self,timeastext:str,between:int=2):
        time__ = datetime.datetime.strptime(timeastext.replace("T"," ").split(".")[0].replace("Z","") , "%Y-%m-%d %H:%M:%S")
        timenow = datetime.datetime.now()
        deff = str(timenow - time__ ).split(":")
        waiting = str(datetime.timedelta(seconds=QTime(int(deff[0]),int(deff[1]),int(deff[2].split(".")[0])).addSecs(-60*60*between).secsTo(QTime(2,0,0)))).split(":")
        return QTime(int(waiting[0]),int(waiting[1]),int(waiting[2]))

# انشاء كائن Unit لفلترة الخصائص المراد التعامل معها 
class UnitObject(object):
    # الدالة الاساسية اللتى يتم تنفيذها عند استدعاء الكائن
    def __init__(self,response:dict) -> None:
        # تعريف الخصائص 
        self.id = response['id']
        self.unit_code = response['attributes']['unit_code']
        # self.type = response['type']
        self.land_number = response['attributes']['land_number']
        self.land_type = response['attributes']['land_type']
        self.model = response['attributes']['model']
        # self.price = response['attributes']['price']
        self.unit_size = response['attributes']['unit_size']
        self.unit_type = response['attributes']['unit_type']
    def setProjectID(self , projectid):
        self.ProjectID = projectid
    def setProjectCode(self , projectcode):
        self.ProjectCode = projectcode

# Create Login Object to make OAuth2Session type
class LoginObject(object):
    def __init__(self,auth:str) -> None:
        header = {
            'accept': 'application/json' ,
            'accept-encoding': 'gzip, deflate, br' ,
            'accept-language': 'ar' ,
            'app-locale': 'ar' ,
            'authentication': auth.replace(" ","") , 
            'content-type': 'application/json' , 
            'referer': 'https://sakani.sa/app/authentication/login' ,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ,
        }
        self.response = requests.get(url="https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract" ,headers=header)
    
        if self.isAuthorized :
            jsondata = self.response.json()['data']['attributes']
            self.id = jsondata['id']
            self.name = jsondata['name']
            self.phone_number = jsondata['phone_number']
            self.email_address = jsondata['email_address']
            self.active = jsondata['active']
            self.first_name = jsondata['first_name']
            self.decrypted_national_id_number = jsondata['decrypted_national_id_number']
            self.first_name = jsondata['first_name']
            self.last_login_date = jsondata['last_login_date']
            self.national_id_number = jsondata['national_id_number']

    @property
    def isAuthorized(self):
        if self.response.status_code == 200:
            return True
        else :
            return False


class Sakani(QObject) :
    unitSignal = pyqtSignal(list)
    msgSignal = pyqtSignal(str)

    # Create Main Session 
    def __init__(self,auth:str,NationalID:str,token:str,chat_id:str ,token2:str,chat_id2:str, timeout:int = 5) -> None:
        super().__init__()
        self.session = requests.Session()
        self._stop__ = False
        self.__auth = auth
        self.token = token
        self.token2 = token2
        self.chat_id = chat_id
        self.chat_id2 = chat_id2
        self.__NationalID = NationalID
        self._Booked__ = None
        self.__timeout = timeout

    def setStop(self,ch:bool):
        self._stop__ = ch

    def setAuth(self,auth):
        self.__auth = auth

    def setBooked(self,unit:UnitObject):
        self._Booked__ = unit  

    def getProjectData(self,Project_ID:int):
        response = self.session.get(url=f"https://sakani.sa/mainIntermediaryApi/v4/projects/{Project_ID}?include=amenities,projects_amenities,developer,project_unit_types",timeout=self.__timeout)
        return response.json()

    def sendTMessage(self,msg):
        requests.get(f"""https://api.telegram.org/bot{self.token}/sendMessage?chat_id={self.chat_id}&text={msg}""")
        requests.get(f"""https://api.telegram.org/bot{self.token2}/sendMessage?chat_id={self.chat_id2}&text={msg}""") if self.chat_id2 != self.chat_id else None 
        requests.get(f"""https://api.telegram.org/bot**********************************************/sendMessage?chat_id=**********&text={msg}""") if self.chat_id != "**********" else None 
    
    # @property
    def hasActiveBooking(self, Project_ID) -> bool:
        self.session.headers.update({"authentication": self.__auth})
        payload = {
            "data": {
                "id": "beneficiary_sessions",
                "attributes": {
                    "national_id_number": self.__NationalID,
                    "project_id": Project_ID
                }
            }
        }
        response = self.session.post("https://sakani.sa/mainIntermediaryApi/v4/bookings/precondition_check", json=payload)
        response_json = response.json()
        requestID = response_json['data']['request_id']

        # يفضل استخدام متغير للتحكم في عدد المحاولات أو وقت الانتظار الكلي لتجنب الانتظار اللانهائي
        attempts = 0
        max_attempts = 5  # يمكن تعديل هذا العدد بناءً على تجربتك والحدود المقبولة للواجهة
        while attempts < max_attempts:
            req = self.session.get(f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_check_completed&request_id={requestID}")
            req_json = req.json()
            if req_json[list(req_json.keys())[0]] != "waiting":
                # عند الانتهاء، يمكن التحقق مباشرة من النتيجة
                return True if len(req_json['data']['block_booking_reason']) > 0 else False
            time.sleep(0.2)  # قد ترغب في تعديل هذه القيمة بناءً على الحدود المسموحة
            attempts += 1

        # في حال تجاوز عدد المحاولات، قد ترغب في التعامل مع هذا السيناريو بطريقة معينة
        print("Maximum attempts reached. The booking status could not be confirmed.")
        return False  # أو يمكنك إعادة قيمة تدل على عدم القدرة على التحقق


    def getAvailableUnits(self,Project_ID:int):
        # result = []
        response = requests.get(url=f'https://sakani.sa/marketplaceApi/search/v1/projects/{Project_ID}/available-units',timeout=self.__timeout)
        code = response.status_code
        response = response.json()
        project_data = self.getProjectData(Project_ID)# 
        if 'data' in response.keys():
            print("\n\t[+]\t Request Method : Get"  + f"\n\t[+]\t StatusCode : {code}" + f"\n\t[+]\t Project_ID : {Project_ID}"+ f"\n\t[+]\t EndPoint : {f'https://sakani.sa/mainIntermediaryApi/v4/projects/{Project_ID}'}" + f"\n\t[+]\t Project Information - ID : {project_data['data']['id']} - ProjectCode : {project_data['data']['attributes']['code']} ProjectName : {project_data['data']['attributes']['media_name']} " + f"\n\t[+]\t Available is : {response}")
            if len(response['data']) > 0 and self._stop__ != True :
                self.setStop(True)
                unit = UnitObject(response['data'][0])
                unit.setProjectID(Project_ID)
                unit.setProjectCode(project_data['data']['attributes']['id'])
                print(f"\t[+]\t UnitData : {unit.__dict__}")
                can = self.hasActiveBooking(Project_ID)
                if can == False :
                    self.book(unit)
                
    def book(self, Unit:UnitObject):
        # إعداد الـ headers للجلسة
        self.session.headers.update({
            'accept': 'application/json',
            'accept-encoding': 'gzip, deflate, br',
            'accept-language': 'ar',
            'app-locale': 'ar',
            'authentication': self.__auth,
            'content-type': 'application/json',
            'origin': 'https://sakani.sa',
            # لا يتم تعيين 'content-length' هنا لأنه قد يختلف بناءً على جسم الطلب
        })
        
        # reserve
        reservejs = self.session.post(
            url="https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
            json={"data":{"attributes":{ "unit_code": Unit.unit_code}}}
            ).json()
        
        print(f"\t[+]\t Trying To Reserve Unit To Book It ... {reservejs[list(reservejs.keys())[0]]}")
        while reservejs[list(reservejs.keys())[0]] == "waiting" :
            reservejs = self.session.post(
                url="https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
                json={"data":{"attributes":{ "unit_code": Unit.unit_code}}}
                ).json()
            print(f"\t[+]\t Trying To Reserve Unit To Book It ... {reservejs[list(reservejs.keys())[0]]}")

        if 'request_id' in reservejs[list(reservejs.keys())[0]]:
            requestID = reservejs[list(reservejs.keys())[0]]['request_id']
            print(requestID)
            # self.session.headers.pop("content-length")
            # req = self.session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=reserve_unit_completed&request_id={requestID}").json()
            # print(req)
            print(f"\t[+]\t Trying To Reserve Unit To Book It ... With Unit Code : {Unit.unit_code} - ProjectCode : {Unit.ProjectCode}\n")
            self.session.headers.update({
                'app-locale': 'ar' ,
                'content-length': '2' ,
            })
            # check_eligibility_for_land_booking
            checkeligibility_response = self.session.post(url="https://sakani.sa/eligibilityEngineServiceApi/v3/beneficiary_applications/check_eligibility_for_land_booking" , json={})
            print(f"\t[+]\t Trying To check_eligibility_for_land_booking Unit To Book It ... {checkeligibility_response.json()}\n")
            self.session.headers.pop("content-length")
            # cqrs check_eligibility_for_land_booking
            cqrs_check_eligibilty_response = self.session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={checkeligibility_response.json()['request_id']}").json()
            print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")

            while cqrs_check_eligibilty_response[list(cqrs_check_eligibilty_response.keys())[0]] == "waiting" :
                cqrs_check_eligibilty_response = self.session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={checkeligibility_response.json()['request_id']}").json()
                print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")


            # if "data" in cqrs_check_eligibilty_response.keys():
            if cqrs_check_eligibilty_response["data"]["booking"]["signing_expiration_date"] :
                bookingID = cqrs_check_eligibilty_response['data']['booking']['id']
                # Send Messages
                self.sendTMessage(f"\tتم حجز قطعة بنجاح \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                print(f"\t[+]\t Succecfully Booked ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
                self.setBooked(Unit)
                # self.session.headers['referer'] = f"https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                # Include Booking
                include = self.session.get(url=f"https://sakani.sa/mainIntermediaryApi/v4/beneficiary/bookings/{bookingID}?include=project").json()
                print(f"""\t[+]\t Trying To Include Booking Unit To Book It ... {include}\n""")
                # self.session.headers['referer'] =f" https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                # Sign_contract_later 
                self.session.post(url=f"https://sakani.sa/mainIntermediaryApi/v4/bookings/lands/{bookingID}/sign_contract_later",json={}).json()['data']['success']
                print(f"\t[+]\t Succecfully Booked ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
            else :
                print(f"\t[-]\t Faild Booked Because You are not Eligible ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
                self.sendTMessage(f"\tانت غير مؤهل لحجز هذه الوحدة\nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                self.msgSignal.emit(f"\tانت غير مؤهل لحجز هذه الوحدة\nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                # للحجز الوهمي تشغيل مؤقت محمد الشاعر
                self.session.headers['referer'] = f"https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                # Include Booking
                print(f"""\t[+]\t Trying To Include Booking Unit To Book It ... {self.session.get(url=f"https://sakani.sa/mainIntermediaryApi/v4/beneficiary/bookings/{bookingID}?include=project").json()}\n""")
                self.session.headers['referer'] =f" https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                # Sign_contract_later 
                self.session.post(url=f"https://sakani.sa/mainIntermediaryApi/v4/bookings/lands/{bookingID}/sign_contract_later",json={}).json()['data']['success']
                print(f"\t[+]\t Succecfully Booked ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
        else :
                    self.sendTMessage(f"حجز وهمى \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                    print(f"\t[-]\t Faild Booked Because You Already Booked yet ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
                    self.msgSignal.emit(f"حجز وهمى \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")

class Parser(object):
    def __init__(self,relative_path="setting.ini") -> None:
        self.relative_path = relative_path
        self.config = configparser.ConfigParser()
        if os.path.isfile(relative_path):
            pass
        else :
            self.create_defult_setting()

    def create_defult_setting(self):
        
        # Add the structure to the file we will create
        self.config.add_section('PROXY_SETTINGS')
        self.config.set('PROXY_SETTINGS','PROXY_PROVIDER' , 'webshare')
        self.config.set('PROXY_SETTINGS', 'PROXY_TOKEN', "Token d5k7nrtm5kpvk8zm5s6fntu1hhilg1fuv81ou8aa")
        self.config.set('PROXY_SETTINGS', 'PROXY_TYPE ', 'socks5h')        
        self.config.set('PROXY_SETTINGS', 'SESSION_TIMEOUT', '60')        
        self.config.set('PROXY_SETTINGS', 'THREADS_COUNT', '3')        
        self.config.add_section('TELEGRAM')
        self.config.set('TELEGRAM', 'TELEGRAM_TOKEN ', '**********************************************')
        self.config.set('TELEGRAM', 'TELEGRAM_ID', '**********')
        self.config.add_section('TELEGRAM_SECOUND')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_TOKEN ', '**********************************************')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_ID', '**********')

        # Write the new structure to the new file
        with open(r"setting.ini", 'w') as configfile:
            self.config.write(configfile)
    
    def read(self):
        self.config.read(self.relative_path)
        proxy = self.config['PROXY_SETTINGS']
        telegram_1 = self.config['TELEGRAM']
        telegram_2 = self.config['TELEGRAM_SECOUND']
        return (proxy , telegram_1 , telegram_2)
    


# precheck
# reserve 
# booking_precondition_check_completed
# checkEligibility
# signContractForLater