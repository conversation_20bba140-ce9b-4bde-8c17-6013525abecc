import undetected_chromedriver as uc
import os
import time
import requests
import random
from mainclass import LoginObject, UnitObject
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from reserve import reserve
from tkinter import messagebox
import threading
import tkinter as tk
import concurrent.futures


class SakaniAutoReserve():
    def __init__(self, units=None, auth_token_var=None, timeout_label=None) -> None:
        self.__stop = False
        os.listdir('.')
        self.units = units
        self.links = []
        self.unit_id = ''
        self.session_timeout = 0
        self.timeout_label = timeout_label
        options = uc.ChromeOptions()
        options.add_argument('--disable-popup-blocking')
        self.driver = uc.Chrome(options=options)
        self.driver.get('https://sakani.sa/app/authentication/login')
        self.auth_token_var = auth_token_var
        if self.auth_token_var.get():
            self.authentication = self.auth_token_var.get()
            self.driver.add_cookie({'name': 'sakani_session', 'value': self.authentication})
        self.authentication = self.get_sakani_authentication()
        print(f"Authentication Token : {self.authentication}")
        self.auth_token_var.set(self.authentication)        
        self.LoginObj = LoginObject(self.authentication, None)
        info = {'Id':self.LoginObj.id , 'Name' : self.LoginObj.name , 'PhoneNumber' : self.LoginObj.phone_number , 'EmailAddress':self.LoginObj.email_address}
        print(f"""Successfully Logged in as : {info}""")

    def update_authentication_token(self, token):
        self.authentication_token = token
        if self.auth_token_var is not None:
            self.auth_token_var.set(token)

    def get_sakani_authentication(self):
        while True:
            try:
                session_cookie =  self.driver.get_cookie('sakani_session')['value']
                break
            except Exception as e:
                time.sleep(1)
        return session_cookie


            
    def sendTMessage(self,msg):
        token  = "5257268449:AAFGA1D_tSip9N-LauskzRLqtqMbYqzxrs8"
        chat_id = "1444072161"
        token2  = "5585475915:AAFdi38LkBjZXYxasacGVOiIrJnIgFVFTE4"
        chat_id_2 = "1161458742"
        print(f"Sending {msg} to telegram" )

        response = requests.get(f"""https://api.telegram.org/bot{token}/sendMessage?chat_id={chat_id}&text={msg}""")
        response = requests.get(f"""https://api.telegram.org/bot{token2}/sendMessage?chat_id={chat_id_2}&text={msg}""") if chat_id_2 != chat_id else None 
        response = requests.get(f"""https://api.telegram.org/bot5257268449:AAFGA1D_tSip9N-LauskzRLqtqMbYqzxrs8/sendMessage?chat_id=1444072161&text={msg}""") if chat_id != "1444072161" else None 

    def update_timer(self):
        if self.session_active and self.session_timeout > 0:
            mins, secs = divmod(self.session_timeout, 60)
            time_format = f'{mins:02d}:{secs:02d}'
            self.timeout_label.config(text=time_format)
            self.session_timeout -= 1
            self.timeout_label.after(1000, self.update_timer)
        elif self.session_timeout == 0:
            self.timeout_label.config(text="Session Timeout", fg="red")

    def open_windows(self):
        for link in self.links:
            self.driver.execute_script(f"window.open('{link}');")
            time.sleep(2)
        self.driver.switch_to.window(self.driver.window_handles[0])
        self.driver.close()
        return self.driver.window_handles
    def reserve_unit(self):
        unit_code = self.unit_id
        reserve(unit_code, self.authentication, self.LoginObj)
    def waiter(self):
        valid_units = []
        
        for unit in self.units:
            try:
                js_code = f"""var callback = arguments[0];
                fetch('https://sakani.sa/mainIntermediaryApi/v4/units/{unit}')
                .then(response => response.json())
                .then(json => callback(json))
                .catch(error => callback({{error: error.message}}));"""
                data_json = self.driver.execute_async_script(js_code)
                if data_json['data']['attributes']['booking_status'] != 'available':
                    print(f'Unit {unit} is not available for booking')
                    continue
                if 'error' in data_json:
                    print(f'Error while validating unit {unit}: {str(data_json)}')
                    continue
                valid_units.append(unit)
                
            except Exception as e:
                print(f'Validation error for unit {unit}: {e}')
                continue

        if not valid_units:
            print("No valid units found.")
            return

        def scan_and_reserve(unit):
            try:
                js_code = f"""var callback = arguments[0];
                fetch('https://sakani.sa/mainIntermediaryApi/v4/units/{unit}')
                .then(response => response.json())
                .then(json => callback(json))
                .catch(error => callback({{error: error.message}}));"""
                data_json = self.driver.execute_async_script(js_code)
                if data_json['data']['attributes']['booking_status'] != 'available':
                    raise Exception(f'Unit {unit} is not available for booking')
                if 'error' in data_json:
                    raise Exception(f'Error while validating unit {unit}: {str(data_json)}')
                
                return unit
            except Exception as e:
                print(f'Error while scanning unit {unit}: {e}')
                return None

        while True:
            start_time = time.time()
            try:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future_to_project = {executor.submit(scan_and_reserve, unit): unit for unit in valid_units}
                    for future in concurrent.futures.as_completed(future_to_project):
                        unit_id = future.result()
                        if unit_id:
                            self.unit_id = unit_id
                            print(f'Chosen unit {future_to_project[future]} reserving...')
                            return
                print(f'Searching for units, time took is {time.time() - start_time}')
                time.sleep(1)
            except Exception as e:
                print(f'Encountered error {e}')




def main():
    global sakani
    sakani = None
    projects = []

    def add_unit():
        unit_id = unit_id_entry.get()
        if unit_id:
            projects.append(unit_id)
            unit_id.delete(0, tk.END)
            unit_listbox.insert(tk.END, unit_id)
    
    def update_authentication_token(*args):
        global sakani
        token = auth_token.get()
        if sakani is not None:
            sakani.update_authentication_token(token)

    # def reserve_unit():
    #     global sakani
    #     unit_id = unit_id_entry.get()
    #     if not sakani:
    #         sakani = SakaniAutoReserve(auth_token_var=auth_token, timeout_label=timeout_label)
    #     sakani.unit_id = unit_id
    #     sakani.reserve_unit()
    #     unit_entry.delete(0, tk.END)

    def start_process():
        def process():
            global sakani
            sakani = SakaniAutoReserve(projects, auth_token_var=auth_token, timeout_label=timeout_label)
            sakani.waiter()
            sakani.reserve_unit()
            messagebox.showinfo("Info", "Process finished")

        threading.Thread(target=process).start()

    root = tk.Tk()
    root.title("Sakani Unit Code reserving")

    unit_frame = tk.Frame(root)
    unit_frame.pack(padx=10, pady=10)

    unit_id_label = tk.Label(unit_frame, text="Enter Unit ID:")
    unit_id_label.pack(side=tk.LEFT)

    unit_id_entry = tk.Entry(unit_frame)
    unit_id_entry.pack(side=tk.LEFT)

    add_button = tk.Button(unit_frame, text="Add", command=add_unit)
    add_button.pack(side=tk.LEFT)

    unit_listbox = tk.Listbox(root, height=10, width=50)
    unit_listbox.pack(padx=10, pady=10)

    unit_frame = tk.Frame(root)
    unit_frame.pack(padx=10, pady=10)

    
    token_frame = tk.Frame(root)
    token_frame.pack(padx=10, pady=10)

    token_label = tk.Label(token_frame, text="Authentication Token")
    token_label.pack(side=tk.LEFT)

    auth_token = tk.StringVar()
    auth_token.trace_add("write", update_authentication_token)

    token_entry = tk.Entry(token_frame, textvariable=auth_token)
    token_entry.pack(side=tk.LEFT)

    start_button = tk.Button(root, text="Start", command=start_process)
    start_button.pack(pady=10)
    
    timer_frame = tk.Frame(root)
    timer_frame.pack(padx=10, pady=10)


    timer_interval = tk.IntVar(value='00:00')
    
    timeout_label = tk.Label(root, text="00:00", font=("Helvetica", 16), textvariable=timer_interval)
    timeout_label.pack(pady=10)


    root.mainloop()

if __name__ == "__main__":
    main()
