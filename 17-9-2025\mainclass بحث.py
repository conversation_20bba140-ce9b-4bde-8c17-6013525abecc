# استدعاء المكتبات الاساسية
import typing
from Packages import (
    pyqtSignal,
    QObject,
    QTime
)
import subprocess
import http.client
import json
import gzip
from urllib.parse import urlparse
import random as rm
import configparser
import os
import datetime
import time
from bs4 import BeautifulSoup
from sakani_updater import SakaniUpdaterThread


updater = SakaniUpdaterThread()
updater.start()

class HttpClientResponseWrapper:
    """A wrapper to make http.client response behave like requests.Response."""

    def __init__(self, status: int, body: bytes, headers: http.client.HTTPMessage):
        self.status_code = status
        self._body = body
        self.headers = headers
        self.text = None
        try:
            self.text = body.decode('utf-8')
        except UnicodeDecodeError:
            pass

    def json(self):
        """Parses the response body as JSON."""
        if self.text is None:
            raise json.JSONDecodeError(
                "Cannot decode JSON from an empty or non-UTF-8 body.", "", 0)
        return json.loads(self.text)


class TimeObject(QObject):  # "2025-03-12T15:42:52.323Z"
    timeoutSignal = pyqtSignal()

    def translateLoginTime(self, timeastext: str, between: int = 2):
        time__ = datetime.datetime.strptime(timeastext.replace(
            "T", " ").split(".")[0].replace("Z", ""), "%Y-%m-%d %H:%M:%S")
        timenow = datetime.datetime.now()
        deff = str(timenow - time__).split(":")
        waiting = str(datetime.timedelta(seconds=QTime(int(deff[0]), int(
            deff[1]), int(deff[2].split(".")[0])).addSecs(-60 * 60 * between).secsTo(QTime(2, 0, 0)))).split(":")
        return QTime(int(waiting[0]), int(waiting[1]), int(waiting[2]))


class UnitObject(object):
    def __init__(self, response: dict) -> None:
        self.id = response['id']
        self.unit_code = response['attributes']['unit_code']
        self.land_number = response['attributes']['land_number']
        self.land_type = response['attributes']['land_type']
        self.model = response['attributes']['model']
        self.unit_size = response['attributes']['unit_size']
        self.unit_type = response['attributes']['unit_type']

    def setProjectID(self, projectid):
        self.ProjectID = projectid

    def setProjectCode(self, projectcode):
        self.ProjectCode = projectcode

def solve_captcha_with_exe(captcha_url: str, user_agent: str) -> str | None:
    """
    Runs the solver.exe to get a reCAPTCHA token.

    Args:
        captcha_url: The full captcha anchor URL.
        user_agent: The User-Agent string to use.

    Returns:
        The solved token as a string, or None if it fails.
    """
    exe_path = "solver_cli.exe" 
    
    if not os.path.exists(exe_path):
        print(f"Error: Executable not found at '{exe_path}'")
        return None

    command = [exe_path, captcha_url, user_agent]
    
    # command.append("--enterprise")

    print(f"Running command: {' '.join(command)}")

    try:
        # Execute the command
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )

        # The token is in stdout. Use .strip() to remove any trailing newlines.
        token = result.stdout.strip()
        token = token.split('**********\n')[1]
        print("Successfully captured token!")
        print(token)
        return token

    except subprocess.CalledProcessError as e:
        # This block runs if the .exe exits with an error (check=True)
        print("Error: The solver executable failed.")
        print(f"Return Code: {e.returncode}")
        # The error messages from your exe are in stderr
        print(f"Error Output:\n{e.stderr}")
        return None
    except FileNotFoundError:
        print(f"Error: Could not find the executable at '{exe_path}'. Make sure the path is correct.")
        return None

class LoginObject(object):
    def __init__(self, auth: str) -> None:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }
        headers.update(updater.cookie_header)
        url = "https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract"
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path + "?" + parsed_url.query

        conn = http.client.HTTPSConnection(host)
        try:
            conn.request("GET", path, headers=headers)
            res = conn.getresponse()
            body = res.read()
            if res.getheader('Content-Encoding') == 'gzip':
                body = gzip.decompress(body)
            self.response = HttpClientResponseWrapper(res.status, body, res.getheaders())
        finally:
            conn.close()

        if self.isAuthorized:
            jsondata = self.response.json()['data']['attributes']
            self.id = jsondata['id']
            self.name = jsondata['name']
            self.phone_number = jsondata['phone_number']
            self.email_address = jsondata['email_address']
            self.active = jsondata['active']
            self.first_name = jsondata['first_name']
            self.decrypted_national_id_number = jsondata['decrypted_national_id_number']
            self.last_login_date = jsondata['last_login_date']
            self.national_id_number = jsondata['national_id_number']

    @property
    def isAuthorized(self) -> bool:
        return self.response.status_code == 200


class Sakani(QObject):
    unitSignal = pyqtSignal(list)
    msgSignal = pyqtSignal(str)

    def __init__(self, auth: str, NationalID: str, token: str, chat_id: str, token2: str, chat_id2: str, timeout: int = 5) -> None:
        super().__init__()
        self._stop__ = False
        self.__auth = auth
        self.token = token
        self.token2 = token2
        self.chat_id = chat_id
        self.chat_id2 = chat_id2
        self.__NationalID = NationalID
        self._Booked__ = None
        self.__timeout = timeout
        
        # Persistent headers, similar to requests.Session
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }

    def _make_request(self, method: str, url: str, extra_headers: dict = None, json_payload: dict = None) -> HttpClientResponseWrapper:
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path
        if parsed_url.query:
            path += "?" + parsed_url.query
        
        # Start with persistent headers and update with request-specific headers
        final_headers = self.headers.copy()
        if extra_headers:
            final_headers.update(extra_headers)
        final_headers.update(updater.cookie_header)
        conn = http.client.HTTPSConnection(host, timeout=self.__timeout)

        body = None
        if json_payload is not None:
            body = json.dumps(json_payload).encode('utf-8')
            # --- FIX STARTS HERE ---
            # Set Content-Type and Content-Length for POST/PUT requests with a JSON body
            final_headers['Content-Type'] = 'application/json'
            # --- FIX ENDS HERE ---
            final_headers['Content-Length'] = str(len(body))

        try:
            conn.request(method, path, body=body, headers=final_headers)
            res = conn.getresponse()
            response_body = res.read()

            if res.getheader('Content-Encoding') == 'gzip':
                response_body = gzip.decompress(response_body)

            return HttpClientResponseWrapper(res.status, response_body, res.getheaders())
        finally:
            conn.close()

    def setStop(self, ch: bool):
        self._stop__ = ch

    def setAuth(self, auth):
        self.__auth = auth
        self.headers['authentication'] = self.__auth # Update persistent header

    def setBooked(self, unit: UnitObject):
        self._Booked__ = unit

    def validate_captcha(self, action: str, unit_code: str, project_id: int) -> str | None:
        """
        Validates captcha using the new Sakani captcha endpoint.

        Args:
            action: The action being performed (e.g., "start_booking")
            unit_code: The unit code for the booking
            project_id: The project ID

        Returns:
            The recaptcha_auth_token if successful, None otherwise
        """
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

        # First, solve the captcha using the existing method
        captcha_url = "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI&co=aHR0cHM6Ly9zYWthbmkuc2E6NDQz&hl=ar&v=Lu6n5xwy2ghvnPNo3IxkhcCb&size=invisible&anchor-ms=20000&execute-ms=15000&cb=7zfyjqjemyzq"
        recaptcha_challenge = solve_captcha_with_exe(captcha_url, user_agent)

        if not recaptcha_challenge:
            print("Failed to solve captcha")
            return None

        # Generate a random identifier (similar to the one in your curl example)
        import hashlib
        import uuid
        identifier = hashlib.md5(str(uuid.uuid4()).encode()).hexdigest().upper()

        # Prepare the payload for the captcha validation endpoint
        payload = {
            "data": {
                "attributes": {
                    "action": action,
                    "identifier": identifier,
                    "recaptcha_challenge": recaptcha_challenge,
                    "recaptcha_type": "grecaptchaV3",
                    "payload": {
                        "data": {
                            "attributes": {
                                "unit_code": unit_code,
                                "project_id": project_id
                            }
                        }
                    },
                    "site_key": "6LfcCh8pAAAAAN5MgtopKxl-4Gon3-MKg2RPqmXI"
                }
            }
        }

        # Set up headers for the captcha validation request
        captcha_headers = {
            'accept': 'application/json',
            'accept-language': 'ar',
            'app-locale': 'ar',
            'content-type': 'application/json',
            'origin': 'https://sakani.sa',
            'priority': 'u=1, i',
            'referer': f'https://sakani.sa/app/units/{project_id}',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': user_agent
        }

        try:
            # Make the captcha validation request
            response = self._make_request(
                'POST',
                'https://sakani.sa/captchaApi/grecaptcha/validate',
                extra_headers=captcha_headers,
                json_payload=payload
            )

            if response.status_code == 200:
                response_data = response.json()
                recaptcha_auth_token = response_data.get('data', {}).get('attributes', {}).get('recaptcha_auth_token')

                if recaptcha_auth_token:
                    print(f"Successfully obtained recaptcha_auth_token")
                    return recaptcha_auth_token
                else:
                    print("No recaptcha_auth_token in response")
                    print(f"Response: {response_data}")
                    return None
            else:
                print(f"Captcha validation failed with status code: {response.status_code}")
                print(f"Response: {response.text}")
                return None

        except Exception as e:
            print(f"Error during captcha validation: {e}")
            return None

    def getProjectData(self, Project_ID: int):
        url = f"https://sakani.sa/mainIntermediaryApi/v4/projects/{Project_ID}?include=amenities,projects_amenities,developer,project_unit_types"
        response = self._make_request('GET', url, extra_headers=self.headers)
        return response.json()

    def sendTMessage(self, msg):
        import requests
        requests.get(f"""https://api.telegram.org/bot{self.token}/sendMessage?chat_id={self.chat_id}&text={msg}""")
        if self.chat_id2 != self.chat_id:
            requests.get(f"""https://api.telegram.org/bot{self.token2}/sendMessage?chat_id={self.chat_id2}&text={msg}""")
        if self.chat_id != "**********":
            requests.get(f"""https://api.telegram.org/bot**********************************************/sendMessage?chat_id=**********&text={msg}""")

    def hasActiveBooking(self, Project_ID) -> bool:
        payload = {
            "data": {
                "id": "beneficiary_sessions",
                "attributes": { "national_id_number": self.__NationalID, "project_id": Project_ID }
            }
        }
        # self.headers already has the auth token, no need to pass it as extra
        response = self._make_request('POST', "https://sakani.sa/mainIntermediaryApi/v4/bookings/precondition_check", json_payload=payload, extra_headers=self.headers)
        response_json = response.json()
        requestID = response_json['data']['request_id']

        attempts, max_attempts = 0, 5
        while attempts < max_attempts:
            req = self._make_request('GET', f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_check_completed&request_id={requestID}", extra_headers=self.headers)
            req_json = req.json()
            if req_json.get(list(req_json.keys())[0]) != "waiting":
                return True if len(req_json.get('data', {}).get('block_booking_reason', [])) > 0 else False
            time.sleep(0.2)
            attempts += 1

        print("Maximum attempts reached. The booking status could not be confirmed.")
        return False

    def getAvailableUnits(self, Project_ID: int):
        url = f'https://sakani.sa/marketplaceApi/search/v1/projects/{Project_ID}/available-units'
        response = self._make_request('GET', url, extra_headers=self.headers)
        code = response.status_code
        response_json = response.json()
        project_data = self.getProjectData(Project_ID)
        
        if 'data' in response_json:
            print(f"\n\t[+]\t Request Method : Get\n\t[+]\t StatusCode : {code}\n\t[+]\t Project_ID : {Project_ID}\n\t[+]\t EndPoint : {f'https://sakani.sa/mainIntermediaryApi/v4/projects/{Project_ID}'}\n\t[+]\t Project Information - ID : {project_data['data']['id']} - ProjectCode : {project_data['data']['attributes']['code']} ProjectName : {project_data['data']['attributes']['media_name']} \n\t[+]\t Available is : {response_json}")
            if len(response_json['data']) > 0 and not self._stop__:
                self.setStop(True)
                unit = UnitObject(response_json['data'][0])
                unit.setProjectID(Project_ID)
                unit.setProjectCode(project_data['data']['attributes']['id'])
                print(f"\t[+]\t UnitData : {unit.__dict__}")
                can = self.hasActiveBooking(Project_ID)
                if not can:
                    self.book(unit)

    def book(self, Unit: UnitObject):
        # First, validate captcha using the new endpoint
        recaptcha_auth_token = self.validate_captcha("start_booking", Unit.unit_code, Unit.ProjectID)

        if not recaptcha_auth_token:
            print("Failed to get recaptcha_auth_token, cannot proceed with booking")
            self.sendTMessage(f"فشل في التحقق من الكابتشا للوحدة\nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
            return

        # reserve
        reserve_payload = {
            "data": {
                "attributes": {
                    "unit_code": Unit.unit_code
                }
            },
            "recaptcha_auth_token": recaptcha_auth_token
        }
        reservejs_response = self._make_request("POST", "https://sakani.sa/mainIntermediaryApi/v4/units/reserve", json_payload=reserve_payload, extra_headers=self.headers)
        reservejs = reservejs_response.json()
        
        print(f"\t[+]\t Trying To Reserve Unit To Book It ... {reservejs.get(list(reservejs.keys())[0])}")
        while reservejs.get(list(reservejs.keys())[0]) == "waiting":
            time.sleep(0.2)
            reservejs_response = self._make_request("POST", "https://sakani.sa/mainIntermediaryApi/v4/units/reserve", json_payload=reserve_payload, extra_headers=self.headers)
            reservejs = reservejs_response.json()
            print(f"\t[+]\t Trying To Reserve Unit To Book It ... {reservejs.get(list(reservejs.keys())[0])}")

        if 'request_id' in reservejs.get(list(reservejs.keys())[0], {}):
            requestID = reservejs[list(reservejs.keys())[0]]['request_id']
            print(requestID)
            print(f"\t[+]\t Trying To Reserve Unit To Book It ... With Unit Code : {Unit.unit_code} - ProjectCode : {Unit.ProjectCode}\n")
            
            # check_eligibility_for_land_booking
            checkeligibility_response = self._make_request("POST", "https://sakani.sa/eligibilityEngineServiceApi/v3/beneficiary_applications/check_eligibility_for_land_booking", json_payload={}, extra_headers=self.headers)
            print(f"\t[+]\t Trying To check_eligibility_for_land_booking Unit To Book It ... {checkeligibility_response.json()}\n")
            
            # cqrs check_eligibility_for_land_booking
            cqrs_url = f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={checkeligibility_response.json()['request_id']}"
            cqrs_check_eligibilty_response = self._make_request('GET', cqrs_url, extra_headers=self.headers).json()
            print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")

            while cqrs_check_eligibilty_response.get(list(cqrs_check_eligibilty_response.keys())[0]) == "waiting":
                time.sleep(0.2)
                cqrs_check_eligibilty_response = self._make_request('GET', cqrs_url, extra_headers=self.headers).json()
                print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")

            if cqrs_check_eligibilty_response.get("data", {}).get("booking", {}).get("signing_expiration_date"):
                bookingID = cqrs_check_eligibilty_response['data']['booking']['id']
                self.sendTMessage(f"\tتم حجز قطعة بنجاح \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                print(f"\t[+]\t Succecfully Booked ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
                self.setBooked(Unit)

                referer_header = {'referer': f"https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"}
                
                include_url = f"https://sakani.sa/mainIntermediaryApi/v4/beneficiary/bookings/{bookingID}?include=project"
                include = self._make_request('GET', include_url, extra_headers=referer_header).json()
                print(f"""\t[+]\t Trying To Include Booking Unit To Book It ... {include}\n""")

                sign_url = f"https://sakani.sa/mainIntermediaryApi/v4/bookings/lands/{bookingID}/sign_contract_later"
                self._make_request('POST', sign_url, extra_headers=referer_header, json_payload={})
                print(f"\t[+]\t Succecfully Booked ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
            else:
                print(f"\t[-]\t Faild Booked Because You are not Eligible ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
                self.sendTMessage(f"\tانت غير مؤهل لحجز هذه الوحدة\nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
                self.msgSignal.emit(f"\tانت غير مؤهل لحجز هذه الوحدة\nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
        else:
            self.sendTMessage(f"حجز وهمى \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")
            print(f"\t[-]\t Faild Booked Because You Already Booked yet ProjectID:{Unit.ProjectID}\n\t[+]\tUnitCode:{Unit.unit_code}\n")
            self.msgSignal.emit(f"حجز وهمى \nرقم المخطط ProjectID : {Unit.ProjectID} \n كود القطعة UnitID : {Unit.id} \n رمز الوحدة UnitCode : {Unit.unit_code}")


class Parser(object):
    def __init__(self, relative_path="setting.ini") -> None:
        self.relative_path = relative_path
        self.config = configparser.ConfigParser()
        if not os.path.isfile(relative_path):
            self.create_defult_setting()

    def create_defult_setting(self):
        self.config.add_section('PROXY_SETTINGS')
        self.config.set('PROXY_SETTINGS', 'PROXY_PROVIDER', 'webshare')
        self.config.set('PROXY_SETTINGS', 'PROXY_TOKEN', "Token d5k7nrtm5kpvk8zm5s6fntu1hhilg1fuv81ou8aa")
        self.config.set('PROXY_SETTINGS', 'PROXY_TYPE ', 'socks5h')
        self.config.set('PROXY_SETTINGS', 'SESSION_TIMEOUT', '60')
        self.config.set('PROXY_SETTINGS', 'THREADS_COUNT', '3')
        self.config.add_section('TELEGRAM')
        self.config.set('TELEGRAM', 'TELEGRAM_TOKEN ', '**********************************************')
        self.config.set('TELEGRAM', 'TELEGRAM_ID', '**********')
        self.config.add_section('TELEGRAM_SECOUND')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_TOKEN ', '**********************************************')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_ID', '**********')

        with open(self.relative_path, 'w') as configfile:
            self.config.write(configfile)

    def read(self):
        self.config.read(self.relative_path)
        proxy = self.config['PROXY_SETTINGS']
        telegram_1 = self.config['TELEGRAM']
        telegram_2 = self.config['TELEGRAM_SECOUND']
        return (proxy, telegram_1, telegram_2)

# precheck
# reserve
# booking_precondition_check_completed
# checkEligibility
# signContractForLater