import http.client
import json
from urllib.parse import urlparse
import gzip
from sakani_updater import SakaniUpdaterThread


class HttpClientResponseWrapper:
    """A wrapper to make http.client response behave like requests.Response."""
    def __init__(self, status: int, body: bytes):
        self.status_code = status
        self._body = body
        self.text = None
        try:
            self.text = body.decode('utf-8')
        except UnicodeDecodeError:
            # Handle cases where the body might not be utf-8, or is empty
            pass

    def json(self):
        """Parses the response body as JSON."""
        if self.text is None:
            raise json.JSONDecodeError("Cannot decode JSON from an empty or non-UTF-8 body.", "", 0)
        return json.loads(self.text)

class LoginObject(object):
    sakani_updater = SakaniUpdaterThread()
    sakani_updater.start()
    
    def __init__(self, auth: str) -> None:

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }
        # add updater cookies to headers
        headers.update(self.sakani_updater.cookie_header)
        
        url = "https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract"
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path + "?" + parsed_url.query

        conn = http.client.HTTPSConnection(host)
        try:
            conn.request("GET", path, headers=headers, )
            res = conn.getresponse()
            body = res.read()
            if res.getheader('Content-Encoding') == 'gzip':
                body = gzip.decompress(body)
            # Wrap the http.client response to be compatible with the old code
            self.response = HttpClientResponseWrapper(res.status, body)
        finally:
            conn.close()
        
        if self.isAuthorized:
            jsondata = self.response.json()['data']['attributes']
            self.id = jsondata['id']
            self.name = jsondata['name']
            self.phone_number = jsondata['phone_number']
            self.email_address = jsondata['email_address']
            self.active = jsondata['active']
            self.first_name = jsondata['first_name']
            self.decrypted_national_id_number = jsondata['decrypted_national_id_number']
            self.last_login_date = jsondata['last_login_date']
            self.national_id_number = jsondata['national_id_number']

    @property
    def isAuthorized(self) -> bool:
        """Checks if the response status code is 200 OK."""
        return self.response.status_code == 200

def main():
    auth = LoginObject('***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
    print(auth)






if __name__ == "__main__":
    main()