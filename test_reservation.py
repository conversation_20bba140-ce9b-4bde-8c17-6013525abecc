#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Sakani reservation system
This script allows testing the complete reservation flow step by step
"""

import sys
import os
import json
from typing import Optional

# Add the current directory to Python path to import from the main file
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # Import from the main class file
    from mainclass_بحث import Sakani, UnitObject, solve_captcha_with_exe
    print("✅ Successfully imported Sakani classes")
except ImportError as e:
    print(f"❌ Failed to import from mainclass_بحث: {e}")
    print("Trying alternative import...")
    try:
        # Try importing from the 17-9-2025 directory
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "17-9-2025"))
        from mainclass_بحث import Sakani, UnitObject, solve_captcha_with_exe
        print("✅ Successfully imported Sakani classes from 17-9-2025 directory")
    except ImportError as e2:
        print(f"❌ Failed to import: {e2}")
        print("Please make sure the mainclass بحث.py file is accessible")
        sys.exit(1)


class ReservationTester:
    """Test class for Sakani reservation system"""
    
    def __init__(self, auth_token: str, national_id: str):
        """
        Initialize the tester
        
        Args:
            auth_token: Authentication token for Sakani API
            national_id: National ID number
        """
        self.auth_token = auth_token
        self.national_id = national_id
        
        # Initialize Sakani instance with test parameters
        self.sakani = Sakani(
            auth=auth_token,
            NationalID=national_id,
            token="test_token",  # Telegram token (not needed for testing)
            chat_id="test_chat",  # Telegram chat ID (not needed for testing)
            token2="test_token2",
            chat_id2="test_chat2",
            timeout=10
        )
        
        print(f"🔧 Initialized Sakani tester with National ID: {national_id}")
    
    def create_test_unit(self, unit_code: str, project_id: int) -> UnitObject:
        """
        Create a test UnitObject for testing
        
        Args:
            unit_code: The unit code to test
            project_id: The project ID
            
        Returns:
            UnitObject instance for testing
        """
        # Create a mock response structure for UnitObject
        mock_response = {
            'id': 'test_unit_id',
            'attributes': {
                'unit_code': unit_code,
                'land_number': 'test_land_number',
                'land_type': 'test_land_type',
                'model': 'test_model',
                'unit_size': 'test_size',
                'unit_type': 'test_type'
            }
        }
        
        unit = UnitObject(mock_response)
        unit.setProjectID(project_id)
        unit.setProjectCode(f"project_code_{project_id}")
        
        print(f"📦 Created test unit: {unit_code} for project: {project_id}")
        return unit
    
    def test_captcha_validation(self, unit_code: str, project_id: int) -> Optional[str]:
        """Test the captcha validation step"""
        print("\n🔐 Testing Captcha Validation...")
        print("-" * 50)
        
        try:
            recaptcha_token = self.sakani.validate_captcha("start_booking", unit_code, project_id)
            
            if recaptcha_token:
                print(f"✅ Captcha validation successful!")
                print(f"🎫 Recaptcha token: {recaptcha_token[:50]}...")
                return recaptcha_token
            else:
                print("❌ Captcha validation failed!")
                return None
                
        except Exception as e:
            print(f"❌ Error during captcha validation: {e}")
            return None
    
    def test_booking_precondition(self, project_id: int) -> Optional[dict]:
        """Test the booking precondition step"""
        print("\n📋 Testing Booking Precondition...")
        print("-" * 50)
        
        try:
            response = self.sakani.start_booking_precondition(project_id)
            
            if response:
                print("✅ Booking precondition started successfully!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")
                return response
            else:
                print("❌ Booking precondition failed!")
                return None
                
        except Exception as e:
            print(f"❌ Error during booking precondition: {e}")
            return None
    
    def test_precondition_status(self, request_id: str, project_id: int) -> Optional[dict]:
        """Test the precondition status check"""
        print("\n🔍 Testing Precondition Status Check...")
        print("-" * 50)
        
        try:
            response = self.sakani.check_booking_precondition_status(request_id, project_id)
            
            if response:
                print("✅ Precondition status check successful!")
                print(f"📄 Response: {json.dumps(response, indent=2)}")
                
                # Check for blocking reasons
                block_reasons = response.get('data', {}).get('block_booking_reason', [])
                if block_reasons:
                    print(f"⚠️  Booking blocked due to: {block_reasons}")
                else:
                    print("✅ No blocking reasons found!")
                
                # Check for booking token
                booking_token = response.get('data', {}).get('booking_session', {}).get('data', {}).get('attributes', {}).get('booking_token')
                if booking_token:
                    print(f"🎫 Booking token obtained: {booking_token[:50]}...")
                else:
                    print("⚠️  No booking token found in response")
                
                return response
            else:
                print("❌ Precondition status check failed!")
                return None
                
        except Exception as e:
            print(f"❌ Error during precondition status check: {e}")
            return None
    
    def test_unit_reservation(self, unit: UnitObject, recaptcha_token: str) -> bool:
        """Test the unit reservation step"""
        print("\n🏠 Testing Unit Reservation...")
        print("-" * 50)
        
        try:
            # Create reserve payload
            reserve_payload = {
                "data": {
                    "attributes": {
                        "unit_code": unit.unit_code
                    }
                },
                "recaptcha_auth_token": recaptcha_token
            }
            
            print(f"📤 Sending reservation request for unit: {unit.unit_code}")
            response = self.sakani._make_request(
                "POST", 
                "https://sakani.sa/mainIntermediaryApi/v4/units/reserve", 
                json_payload=reserve_payload, 
                extra_headers=self.sakani.headers
            )
            
            if response.status_code == 200:
                response_data = response.json()
                print("✅ Unit reservation request successful!")
                print(f"📄 Response: {json.dumps(response_data, indent=2)}")
                return True
            else:
                print(f"❌ Unit reservation failed with status: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error during unit reservation: {e}")
            return False
    
    def run_complete_test(self, unit_code: str, project_id: int) -> bool:
        """Run the complete reservation test flow"""
        print("🚀 Starting Complete Reservation Test")
        print("=" * 60)
        print(f"Unit Code: {unit_code}")
        print(f"Project ID: {project_id}")
        print(f"National ID: {self.national_id}")
        print("=" * 60)
        
        # Step 1: Test captcha validation
        recaptcha_token = self.test_captcha_validation(unit_code, project_id)
        if not recaptcha_token:
            print("\n❌ Test failed at captcha validation step")
            return False
        
        # Step 2: Test booking precondition
        precondition_response = self.test_booking_precondition(project_id)
        if not precondition_response:
            print("\n❌ Test failed at booking precondition step")
            return False
        
        request_id = precondition_response.get('data', {}).get('request_id')
        if not request_id:
            print("\n❌ No request_id found in precondition response")
            return False
        
        # Step 3: Test precondition status
        status_response = self.test_precondition_status(request_id, project_id)
        if not status_response:
            print("\n❌ Test failed at precondition status step")
            return False
        
        # Check if booking is blocked
        block_reasons = status_response.get('data', {}).get('block_booking_reason', [])
        if block_reasons:
            print(f"\n⚠️  Cannot proceed with reservation due to blocking reasons: {block_reasons}")
            return False
        
        # Step 4: Test unit reservation
        test_unit = self.create_test_unit(unit_code, project_id)
        reservation_success = self.test_unit_reservation(test_unit, recaptcha_token)
        
        if reservation_success:
            print("\n🎉 Complete reservation test PASSED!")
            return True
        else:
            print("\n❌ Test failed at unit reservation step")
            return False


def main():
    """Main function to run the test"""
    print("🧪 Sakani Reservation Tester")
    print("=" * 40)
    
    # Get input from user
    auth_token = input("Enter your authentication token: ").strip()
    national_id = input("Enter your national ID: ").strip()
    unit_code = input("Enter the unit code to test: ").strip()
    project_id = input("Enter the project ID: ").strip()
    
    if not all([auth_token, national_id, unit_code, project_id]):
        print("❌ All fields are required!")
        return
    
    try:
        project_id = int(project_id)
    except ValueError:
        print("❌ Project ID must be a number!")
        return
    
    # Create tester and run test
    tester = ReservationTester(auth_token, national_id)
    success = tester.run_complete_test(unit_code, project_id)
    
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    main()
