from pybench import Test

class IfThen<PERSON>lse(Test):

    version = 2.0
    operations = 30*3 # hard to say...
    rounds = 150000

    def test(self):

        a,b,c = 1,2,3
        for i in range(self.rounds):

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

            if a == 1:
                if b == 2:
                    if c != 3:
                        c = 3
                        b = 3
                    else:
                        c = 2
                elif b == 3:
                    b = 2
                    a = 2
            elif a == 2:
                a = 3
            else:
                a = 1

    def calibrate(self):

        a,b,c = 1,2,3
        for i in range(self.rounds):
            pass

class NestedForLoops(Test):

    version = 2.0
    operations = 1000*10*5
    rounds = 300

    def test(self):

        l1 = range(1000)
        l2 = range(10)
        l3 = range(5)
        for i in range(self.rounds):
            for i in l1:
                for j in l2:
                    for k in l3:
                        pass

    def calibrate(self):

        l1 = range(1000)
        l2 = range(10)
        l3 = range(5)
        for i in range(self.rounds):
            pass

class ForLoops(Test):

    version = 2.0
    operations = 5 * 5
    rounds = 10000

    def test(self):

        l1 = range(100)
        for i in range(self.rounds):
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass

            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass

            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass

            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass

            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass
            for i in l1:
                pass

    def calibrate(self):

        l1 = range(1000)
        for i in range(self.rounds):
            pass
