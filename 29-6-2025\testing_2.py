import nodriver
import asyncio
import logging

# Suppress unnecessary logging from the library
logging.getLogger('uc').setLevel(logging.WARNING)

async def get_sakani_cookie():
    """
    Navigates to the Sakani homepage, waits for the 'cf_clearance' cookie,
    and then exits.
    """
    print("🚀 Starting browser...")
    try:
        browser = await nodriver.start()
        print("Navigating to https://sakani.sa/ ...")
        page = await browser.get('https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract/')
        # The browser is automatically closed here
        print("\n🚪 Browser closed.")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    nodriver.loop().run_until_complete(get_sakani_cookie())