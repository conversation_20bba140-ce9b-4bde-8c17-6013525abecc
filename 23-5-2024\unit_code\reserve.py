import requests
import logging
from mainclass import LoginObject, UnitObject
# __auth = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

timeout = 30
def sendTMessage(msg):
    token  = "5257268449:AAFGA1D_tSip9N-LauskzRLqtqMbYqzxrs8"
    chat_id = "1444072161"
    token2  = "5585475915:AAFdi38LkBjZXYxasacGVOiIrJnIgFVFTE4"
    chat_id_2 = "1161458742"
    print(f"Sending {msg} to telegram" )

    response = requests.get(f"""https://api.telegram.org/bot{token}/sendMessage?chat_id={chat_id}&text={msg}""")
    # response = requests.get(f"""https://api.telegram.org/bot{token2}/sendMessage?chat_id={chat_id_2}&text={msg}""") if chat_id_2 != chat_id else None 
    response = requests.get(f"""https://api.telegram.org/bot5257268449:AAFGA1D_tSip9N-LauskzRLqtqMbYqzxrs8/sendMessage?chat_id=1444072161&text={msg}""") if chat_id != "1444072161" else None 

#شغل دي
def hasActiveBooking(session:requests.Session, Project_ID:int, auth:str, national_id_number)->bool:
    session.headers.update({"authentication": auth})
    requestID = session.post(url="https://sakani.sa/mainIntermediaryApi/v4/bookings/precondition_check" ,json={"data":{"id":"beneficiary_sessions","attributes":{"national_id_number":national_id_number, "project_id":Project_ID}}}).json()['data']['request_id']
    # print(requestID)
    req = session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_check_completed&request_id={requestID}").json()
    # print(req)
    while req[list(req.keys())[0]] == "waiting":
        req = session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_check_completed&request_id={requestID}").json()
        # print(req)
    return True if len(req['data']['block_booking_reason']) > 0 else False


def getUnitWithID(session:requests.Session, UnitID:str) -> UnitObject:
    response = session.get(url=f"https://sakani.sa/mainIntermediaryApi/v4/units/{UnitID}")
    print(f"\t[+]\t UnitData : {response.json()['data']}")
    return UnitObject(response.json()['data'])



def reserve(unitCode, auth, Loginobj:LoginObject):
    # ProjectID = "1004"
    session = requests.session()
    session.headers = {
        'accept': 'application/json' , 
        'accept-encoding': 'gzip, deflate, br' ,
        'accept-language': 'ar' ,
        'app-locale': 'ar'  ,
        'authentication': auth ,#'*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' ,
        'content-type': 'application/json' ,
        'origin': 'https://sakani.sa' ,
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    unit_data = getUnitWithID(session, unitCode)
    print('Trying to reserve', unit_data.__dict__)
    unitCode = unit_data.unit_code
    # شغل دي
    can = hasActiveBooking(session, unit_data.ProjectID, auth, Loginobj.national_id_number)
    #الغي can
    # can = False
    if can == False :
        # reserve
        while True:
            try:
                reservejs = session.post(
                    url="https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
                    json={"data":{"attributes":{ "unit_code": unitCode}}} ,timeout= timeout
                    ).json()

                print(f"Trying To Reserve Unit To Book It ... {reservejs[list(reservejs.keys())[0]]}")
                break
            except:
                print("Error")
                pass
        while True:
            try:
                while reservejs[list(reservejs.keys())[0]] == "waiting" :
                    reservejs = session.post(
                        url="https://sakani.sa/mainIntermediaryApi/v4/units/reserve",
                        json={"data":{"attributes":{ "unit_code": unitCode}}}, timeout=timeout
                        ).json()
                    #check if reservejs has status
                    if 'status' in reservejs.keys():
                        if reservejs['status'] == 500:
                            print(f"Error : {reservejs['message']}")
                            continue
                    print(f"\t[+]\t Trying To Reserve Unit To Book It ... {reservejs[list(reservejs.keys())[0]]}")
                break
            except:
                print("Error")
                pass
        while True:
            try:
                
                if 'request_id' in reservejs[list(reservejs.keys())[0]]:
                    requestID = reservejs[list(reservejs.keys())[0]]['request_id']
                    print(requestID)
                    # self.session.headers.pop("content-length")
                    # req = self.session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=reserve_unit_completed&request_id={requestID}").json()
                    # print(req)
                    print(f"\t[+]\t Trying To Reserve Unit To Book It ... With Unit Code : {unitCode}\n")
                    session.headers.update({
                        'app-locale': 'ar' ,
                        'content-length': '2' ,
                    })
                    # check_eligibility_for_land_booking
                    checkeligibility_response = session.post(url="https://sakani.sa/eligibilityEngineServiceApi/v3/beneficiary_applications/check_eligibility_for_land_booking" , json={}, timeout=timeout)
                    print(f"\t[+]\t Trying To check_eligibility_for_land_booking Unit To Book It ... {checkeligibility_response.json()}\n")
                    break
            except:
                print("Error")
                pass
        while True:
            try:
                if session.headers.get("content-length") != None:
                    session.headers.pop("content-length")
                # cqrs check_eligibility_for_land_booking
                cqrs_check_eligibilty_response = session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={checkeligibility_response.json()['request_id']}", timeout=timeout).json()
                print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")
                while cqrs_check_eligibilty_response[list(cqrs_check_eligibilty_response.keys())[0]] == "waiting" :
                    cqrs_check_eligibilty_response = session.get(url=f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={checkeligibility_response.json()['request_id']}", timeout=timeout).json()
                    print(f"\t[+]\t Trying To CQRS check_eligibility_for_land_booking Unit To Book It ... {cqrs_check_eligibilty_response}\n")
                # if "data" in cqrs_check_eligibilty_response.keys():
                if cqrs_check_eligibilty_response['data']['eligible_status'] == 'eligible' :
                    bookingID = cqrs_check_eligibilty_response['data']['booking']['id']
                    print(f"Succecfully Booked \n\t[+]\tUnitCode:{unitCode}\n")
                    # self.setBooked(Unit)
                    # self.session.headers['referer'] = f"https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                    # Include Booking
                    include = session.get(url=f"https://sakani.sa/mainIntermediaryApi/v4/beneficiary/bookings/{bookingID}?include=project", timeout=timeout).json()
                    print(f"""\t[+]\t Trying To Include Booking Unit To Book It ... {include}\n""")
                    # self.session.headers['referer'] =f" https://sakani.sa/app/booking/land?project_id={Unit.ProjectID}&project_code={Unit.ProjectCode}&unit_id={Unit.id}&project_type=lands_moh_land&ref=pdr8q8pv&booking_id={bookingID}"
                    # Sign_contract_later 
                    session.post(url=f"https://sakani.sa/mainIntermediaryApi/v4/bookings/lands/{bookingID}/sign_contract_later",json={}, timeout=timeout).json()['data']['success']
                    print(f"Succecfully Booked \n\t[+]\tUnitCode:{unitCode}\n")
                    # Send Messages
                    sendTMessage(f"تم حجز قطعة بنجاح \n رمز القطعة UnitCode : {unitCode}")
                    # self.msgSignal.emit(f"تم حجز قطعة بنجاح \n رمز القطعة UnitCode : {unitCode} \n كود القطعة UnitID : {unitID}")
                else :
                    print(f"Faild Booked Because You are not Eligible \n\t[+]\tUnitCode:{unitCode}\n")
                    sendTMessage(f"انت غير مؤهل لحجز هذه الوحدة\n رمز القطعة UnitCode : {unitCode}")
                    # msgSignal.emit(f"انت غير مؤهل لحجز هذه الوحدة\n رمز القطعة UnitCode : {unitCode}")
                break
            except:
                print("Error")
                pass

# while True:
#     try:
#         reserve("970938", auth=__auth)
#     except:
#         print("Error")
#         pass