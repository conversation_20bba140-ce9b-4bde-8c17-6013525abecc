Pyarmor 8.0 End User License Agreement

The following agreement regarding Pyarmor is made between <PERSON><PERSON> -
referred to as "licensor" - and anyone or organization who is installing,
accessing or in any other way using Pyarmor - referred to as "user" or
"you".

1. Definitions
--------------

1.1. "This Software"

    means Pyarmor 8.0+, it doesn't include Pyarmor prior to 8.0. Pyarmor 8.0 is
    rewritten, and provides new features. For compatibility, Pyarmor 8.0 also
    includes most of the old features, this license doesn't apply to those old
    features. It's only for new features.

1.2. "Product"
    means any application or software for sale.

1.3. "One Product"
    means a product name and everything that makes up this name. It includes
    all the devices to develop, build, debug, test product. It also includes
    product current version, history versions and all the future versions.

    One product may has several variants, each variant name is composed of
    product name plus feature name. As long as the proportion of the
    variable part is far less than that of the common part, they're
    considered as "one product".

1.4. "Customer"
    means anyone who uses user's product.

1.5  "Script"
    means Python script.

1.6  "User Script"
    means user owns this script.

1.7  "Other Script"
    means user doesn't own this script.

1.8  "Mix Str"
    means a feature of this software to obfuscate string constant in script

1.9  "BCC Mode"
    means an irreversible obfuscation method, a feature of this software

1.10 "RFT Mode"
    means an irreversible obfuscation method, a feature of this software

1.11 "Big Script"
    means script size exceeds a certain value

1.12 "Pyarmor License"
    means a file issued by licensor to unlock this software limitations

1.13 "Basic License"
    means a kind of Pyarmor License

1.13 "Pro License"
    means a kind of Pyarmor License

1.14 "Group License"
    means a kind of Pyarmor License

1.15 "License No."
    means a string with format "pyarmor-vax-xxxxxx", "x" stands for a
    digital, each Pyarmor License has an unique License No.

1.16 "Licensed Product"
    means product has been registered to one Pyarmor License.

2. License Grants and Conditions
--------------------------------

Installing and using this software signifies acceptance of these terms and
conditions of the license. If you do not agree with the terms of this
license, you must remove all of this software files from your storage
devices and cease to use this software.

2.1. Trial Version Limitations

This software is published as shareware, free trial version never expires,
but there are some limitations.

2.1.1 Can not obfuscate big script
2.1.2 Can not use feature Mix Str
2.1.3 Can not use feature RFT Mode, BCC Mode
2.1.4 Can not be used to obfuscate any commercial product. If the total sale
      income of this product is less than 100 x license fees, this software
      could be used temporarily.
2.1.5 Can not be used to provide obfuscation service in any form, in short
      this software can't be used to obfuscate the scripts of others

2.2. Grants

User purchases Pyarmor License to unlock these limitations except 2.1.5.

Licensor issues 3 kind of Pyarmor License.

2.2.1 Pyarmor Basic, unlock limitations 2.1.1, 2.1.2, 2.1.4
2.2.2 Pyarmor Pro, unlock limitations 2.1.1, 2.1.2, 2.1.3, 2.1.4
2.2.3 Pyarmor Group, unlock limitations 2.1.1, 2.1.2, 2.1.3, 2.1.4

Pyarmor Basic and Pyarmor Pro need internet connection to verify, it doesn't
work in the device without internet connection.

Pyarmor Pro can not be used in CI/CD pipeline.

Pyarmor Group need not internet connection.

Each Pyarmor License only need pay once, not periodically.

2.3 Conditions

2.3.1 Each Pyarmor License can only be used to register One Product. Each
      Licensed Product has an unique License No. in global. If user has many
      products, each product need purchase one Pyarmor License. Except 2.6.1

2.3.2 Pyarmor License could be installed in limited machines and devices which
      used to develop, build, debug, test and support Licensed Product.

2.3.3 Pyarmor Basic and Pro License can only be used in no more than 100
      devices. Pyarmor License be used means use any feature of Pyarmor in
      one machine. Running obfuscated scripts generated by Pyarmor is not
      considered as Pyarmor License be used.

2.3.4 Pyarmor Basic and Pro License need internet connection to verify.

2.3.5 Pyarmor License could not be installed in customer's devices.

2.3.6 Pyarmor License could not be used to obfuscate any other scripts.

2.3.7 Pyarmor License could not be transferred to other product.

2.4 Special Cases

2.4.1 When product name is changed, but the product features are same,
      Pyarmor License could be used without changing registration product
      name.

2.4.2 When organization user is renamed, or acquired by others, only product
      name is not changed, Pyarmor License need change nothing, and still
      could be used. But if product name is changed, even product functions
      are same, Pyarmor License can't be used again.

2.4.3 When product in developing and its name is not defined, the initial
      registration could use "TBD" as the product name. In this case,
      product name can be changed once. Before this product is sold, user
      must change registration name to real product name.

2.5 Modifying Software

User could modify Python scripts of this software to meet needs. But this
modified software could only be used in Licensed Product, it could not be
distributed to others.

2.6 Fair Use

This License is not intended to limit any rights users have under applicable
copyright doctrines of fair use, fair dealing, or other equivalents.

2.6.1 If user has many products, and has purchased one license for the first
      product. The second product could use first product license only if sale
      income of the second product less than 100x license fees. Once greater
      than 100x license fees, the second product need purchase its own license.
      It's same to user's other products.

3. Responsibilities and Commitments
-----------------------------------

3.1 This software does nothing except the features described in the software
    documentation.

3.2 Using Pyarmor Basic and Pro License, this software need internet
    connection to request authorization. And only the related files of
    Pyarmor License, serial number of hard disk, Ethernet address, IPv4/IPv6
    address, hostname will be sent to remote server for verification.

3.3 Except 3.2, this software does not record and collect any other device
    information, and need not connect to internet.

3.4 Regarding to obfuscated scripts generated by this software

3.4.1 this software has no any control or limitation to obfuscated scripts,
      the behaviors of obfuscated scripts are totally defined by user.

3.4.2 License No. and product name will be embedded into obfuscated scripts,
      user's regname, email and other information are not.

4. Termination
--------------

The rights granted under this License will terminate automatically if You
fail to comply with any of its terms.

4.1 You issue chargeback after purchased Pyarmor License has been registered
    to one product. Even chargeback is rejected by bank or user cancels
    chargeback, this Pyarmor License can't be used again.

4.2 You let others got Pyarmor License intentionally or unintentionally

4.3 You lost Pyarmor License by hacker

4.4 In any cases include 4.2, 4.3, once licensor find too many machines use
    one Pyarmor License exceeds permission, this Pyarmor License will be
    ceased, and never could be used again.

4.5 In any cases if licensor finds one Pyarmor License is used to other
    product, even it's caused by 4.3, this Pyarmor License will be ceased. A
    notify email will be sent to registration email of this Pyarmor License.

4.6 Pyarmor license must be installed in your build enviornments, you can't
    install Pyarmor License in customer's device to obfuscate your customer's
    scripts

5. Restrictions
---------------

You shall not (and shall not allow any third party to):

5.1 decompile, disassemble, or otherwise reverse engineer any binary files
    of this software;

5.2 distribute, sell, sublicense, rent, lease, or use this software (or any
    portion thereof) for time sharing, hosting, service provider, or like
    purposes;

5.3 remove any product identification, proprietary, copyright, or other
    notices contained in this software;

5.4 copy, modify (except as expressly permitted in this Agreement) or
    translate any part of this software, create a derivative work of any
    part of this software, or incorporate this software into or with other
    software, except to the extent expressly authorized in writing by
    licensor;

5.5 attempt to circumvent or disable the security key mechanism that
    protects this software against unauthorized use (except and only to the
    extent that applicable law prohibits or restricts such restrictions);

6. Disclaimer of Warranty
-------------------------

This Software is provided under this License on an "as is" basis, without
warranty of any kind, either expressed, implied, or statutory, including,
without limitation, warranties that this software is free of defects, fit
for a particular purpose or non-infringing. The entire risk as to the
quality and performance of this software is with users. Neither the licensor
nor the agents of the licensor will be liable for data loss, damages, loss
of profits or any other kind of loss while using or misusing this
software. This disclaimer of warranty constitutes an essential part of this
License. No use of this software is authorized under this License except
under this disclaimer.

7. Changes to this Agreement
----------------------------

Licensor reserves the right, at its sole discretion, to modify or replace
this Agreement at any time. If a revision is material licensor will provide
at least 30 days' notice prior to any new terms taking effect. What
constitutes a material change will be determined at the sole discretion of
the licensor.

By continuing to access or use this software after any revisions become
effective, You agree to be bound by the revised terms. If You do not agree
to the new terms, You are no longer authorized to use this software.

8. Litigation
-------------

Any litigation relating to this License may be brought only in the courts of
a jurisdiction where the defendant maintains its principal place of business
and such litigation shall be governed by laws of that jurisdiction, without
reference to its conflict-of-law provisions.  Nothing in this Section shall
prevent a party's ability to bring cross-claims or counter-claims.
