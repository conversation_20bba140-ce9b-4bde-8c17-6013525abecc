﻿     PyArmor 最终用户许可协议

     以下关于 PyArmor（下称“本软件”）的协议在 赵俊德（下称“许可人”）和任何安装、
     访问或以其它方式使用本软件的自然人（下称“用户”）之间订立。

  1. 本软件的作者及版权持有人为 赵俊德。

  2. 本软件可以自由试用但是功能上有一定限制:

     a. 试用版本可以加密的脚本大小有限制，超过限制的脚本无法进行加密。

     b. 在试用版中中生成的加密脚本不是私有的，也就是说，其他任何人也可以为这些加
        密脚本生成新的许可文件。

     c. 试用版本不能下载其他平台最新版本的动态库，以前版本的动态库依旧可以使用。

     d. 终极加密模式（SPP）在试用版本中不可用。

     e. 任何人都可以使用本软件加密非商业用途的Python脚本，未经许可不得用于商业用
        途。

  3. 本软件有两种基本类型的许可方式:

     a. 个人用户许可，适用于产品的所有权为个人所有。个人用户购买一个许可证可以在
        自己所有的计算机和相关硬件设备上使用。购买这种类型的许可证的时候，注册名
        称填写个人的真实姓名，本产品只授权于注册名称对应的个人使用。

        个人用户许可证允许使用本软件加密任何属于自己的 Python 脚本，为加密脚本生
        成私有许可文件，发布加密后的脚本和必要的辅助文件到任何其他设备。

        个人用户许可证不允许加密产权属于法人（公司）的 Python 脚本。

     b. 企业用户许可，适用于产品的所有权为法人（公司）所有。企业用户购买一个软件
        许可证可以在同一个产品的各个项目中使用。购买这种类型的许可证的时候，注册
        名称填写机构名称以及产品名称，例如，“西安德新软件的易科系统”，本软件只授
        权于注册名称对应的产品使用。

        同一个产品包括产品升级之后的所有版本。

        企业用户许可证允许使用本软件在任何设备上，加密属于该产品系列的 Python 脚
        本，为加密脚本生成私有许可文件，发布加密后的脚本和必要的辅助文件到任何其
        他设备。

        除非有许可人的许可，否则企业用户许可证不可以用于其他的产品。如果需要在其
        他产品中使用，必须为其他产品单独购买软件许可。

     不管那一种许可方式，本软件都只可用于保护产品本身，不允许应用于产权不属于被
     授权人的 Python 脚本。

  4. 除了购买软件许可的费用之外，没有其他任何费用。获得软件许可的用户可以使用本软
     件在许可的范围之内加密任何Python脚本并自由发布，不需要在向许可人支付任何费用。

  5. 购买软件许可，可以通过下面任意一个链接的电子商务网站

         https://order.shareit.com/cart/add?vendorid=200089125&PRODUCT[300871197]=1
         https://pyarmor.dashingsoft.com/cart/order.html

     对于类型为个人用户的许可，注册名称需要填写正确的姓名。

     对于类型为企业用户的许可，除了注册名称需要填写正确的企业名称之外，还需要填
     写被授权的产品名称，如果仅在企业内部使用，不会用于任何被销售的产品，可以填
     写“内部使用”。

     支付成功之后一个名为 "pyarmor-regcode-xxxx.txt" 的注册文件会自动通过电子邮
     件发送过去，把注册文件保存到磁盘，然后使用下面的命令进行注册

         pyarmor register pyarmor-regcode-xxxx.txt

     运行下面的命令查看注册信息

         pyarmor register

     注册成功之后，请彻底删除使用试用版本生成的所有文件，然后重新进行加密。

     软件注册码永久有效，可以一直使用，但是不能转接或者租用。

  6. 本软件可免费分发（除下列例外），分发前提为分发包未以任何形式被修改：

     a) 任何人未经书面许可的情况下，不得再分发软件包的任何单独部分。

     b) 在未经书面许可的情况下，本软件不得在任何其它软件包中发布。本软件必须保持
        以未经修改的原始安装文件而供下载，而不得对用户附带任何障碍或条件，例如收
        取下载费用，或以用户提供联系信息为前提来提供下载。

     c) 本软件未经修改的安装文件必须以纯净而独立的形式提供。禁止任何形式的捆绑。
        尤其禁止使用任何安装或下载软件来提供任何下载捆绑，除非获得书面形式同意。

     d) 本软件的分发者不得包含、指向或引用黑客/破解、注册文件及注册文件生成器。

     e) 违反上述条件的情况下，许可自动立即失效。

  7. 本软件“按原样”发布。不提供任何明示或暗示的担保。您的使用需要自己承担风险。无
     论作者、许可人或许可人的经销商，均不对使用或误用本软件时发生的数据丢失、损坏、
     利润损失或其它任何形式的损失而负责。

  8. 本软件的二进制代码不得被进行反向工程来重新创建本软件专用的加密算法。

  9. 本协议由许可人负责解释。任何时候许可人对本协议做出任何修改，修改版本自动适用
     于用户。

 10. 安装并使用本软件意味着接受本许可的这些条款和条件。如果您不同意本许可的条款，您必
     须从您的存储设备中删除本软件全部文件并终止使用本软件。
