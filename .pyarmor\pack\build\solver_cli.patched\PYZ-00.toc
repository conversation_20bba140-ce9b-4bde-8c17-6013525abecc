('E:\\Programming\\Python\\sakani\\.pyarmor\\pack\\build\\solver_cli.patched\\PYZ-00.pyz',
 [('__future__',
   'C:\\Program Files\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('anyio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._contextmanagers',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_contextmanagers.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('colorama',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('elitesoftworks',
   'E:\\Programming\\Python\\sakani\\elitesoftworks.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python312\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python312\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'C:\\Program Files\\Python312\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python312\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('httpcore',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('hwid', 'C:\\Program Files\\Python312\\Lib\\hwid.py', 'PYMODULE'),
  ('idna',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python312\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python312\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python312\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python312\\Lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python312\\Lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyarmor_runtime_000000',
   'E:\\Programming\\Python\\sakani\\.pyarmor\\pack\\dist\\pyarmor_runtime_000000\\__init__.py',
   'PYMODULE'),
  ('pyfiglet',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\pyfiglet\\__init__.py',
   'PYMODULE'),
  ('pyfiglet.version',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\pyfiglet\\version.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python312\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python312\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python312\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('sniffio',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Programming\\Python\\sakani\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python312\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile.__main__',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\__main__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python312\\Lib\\zipimport.py', 'PYMODULE')])
