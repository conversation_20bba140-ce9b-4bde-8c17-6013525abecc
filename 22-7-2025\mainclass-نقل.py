# استدعاء المكتبات الاساسية
import typing
from Packages import (
    pyqtSignal,
    QObject,
    QTime
)
import http.client
import json
import gzip
from urllib.parse import urlparse
import requests
import configparser
import os
import random as rm
import datetime
import time
from colorama import init, Fore, Back, Style
from sakani_updater import SakaniUpdaterThread
# Initialize colorama
init(autoreset=True)

def logger(text, plus: bool = True, color: str = Fore.WHITE):
    print(Fore.WHITE + f"\t[{'+' if plus else '-'}]\t" + Fore.RESET + color + f"{text}" + Fore.RESET)
updater = SakaniUpdaterThread()
updater.start()

# --- New Helper Class ---
class HttpClientResponseWrapper:
    """A wrapper to make http.client response behave like requests.Response."""

    def __init__(self, status: int, body: bytes, headers: http.client.HTTPMessage):
        self.status_code = status
        self._body = body
        self.headers = headers
        self.text = None
        try:
            self.text = body.decode('utf-8')
        except UnicodeDecodeError:
            # Body is not valid UTF-8, maybe it's binary content
            pass

    def json(self):
        """Parses the response body as JSON."""
        if self.text is None:
            raise json.JSONDecodeError(
                "Cannot decode JSON from an empty or non-UTF-8 body.", "", 0)
        return json.loads(self.text)

# --- Standalone Request Function for Telegram ---
def _send_telegram_message(url: str, timeout: int = 10):
    """Sends a simple GET request using http.client."""
    import requests
    try:
        requests.get(url, timeout=timeout)
    except Exception as e:
        print(f"Error sending Telegram message: {e}")


class TimeObject(QObject):  # "2023-04-02T23:16:27.859Z"
    timeoutSignal = pyqtSignal()

    def translateLoginTime(self, timeastext: str):
        time__ = datetime.datetime.strptime(timeastext.replace(
            "T", " ").split(".")[0].replace("Z", ""), "%Y-%m-%d %H:%M:%S")
        timenow = datetime.datetime.now()
        deff = str(timenow - time__).split(":")
        waiting = str(datetime.timedelta(seconds=QTime(int(deff[0]), int(
            deff[1]), int(deff[2].split(".")[0])).addSecs(-60 * 60 * 2).secsTo(QTime(3, 0, 0)))).split(":")
        return QTime(int(waiting[0]), int(waiting[1]), int(waiting[2]))

class Proxy(object):
    def __init__(self,response:dict) -> None:
        self.id = response['id']
        self.username = response['username']
        self.password = response['password']
        self.proxy_address = response['proxy_address']
        self.port = response['port']
        self.valid = response['valid']
        self.socks5h_proxy_expression = dict(http = f"socks5h://{self.username}:{self.password}@{self.proxy_address}:{self.port}",https = f"socks5h://{self.username}:{self.password}@{self.proxy_address}:{self.port}")

class BadProxy(BaseException):
    def __init__(self, proxy:str,*args: object) -> None:
        super().__init__(*args)
        self.proxy = proxy

    def __str__(self) -> str:
        return f"Bad Proxy Please Change it {self.proxy}"

class UnitObject(object):
    def __init__(self, response: dict) -> None:
        self.id = response['id']
        self.unit_code = response['attributes']['unit_code']
        self.land_number = response['attributes']['land_number']
        self.land_type = response['attributes']['land_type']
        self.model = response['attributes']['model']
        self.unit_size = response['attributes']['unit_size']
        self.unit_type = response['attributes']['unit_type']
        self.ProjectID = response['attributes']['project_id']

    def setProjectID(self, projectid):
        self.ProjectID = projectid

    def setProjectCode(self, projectcode):
        self.ProjectCode = projectcode


class LoginObject(object):
    def __init__(self, auth: str) -> None:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }
        # update headers with updater cookies
        headers.update(updater.cookie_header)
        
        url = "https://sakani.sa/mainIntermediaryApi/v4/beneficiary/me?include=beneficiary_assets_detail,beneficiary_application,active_booking,active_subsidy_contract"
        
        try:
            parsed_url = urlparse(url)
            conn = http.client.HTTPSConnection(parsed_url.netloc)
            path = f"{parsed_url.path}?{parsed_url.query}"
            conn.request("GET", path, headers=headers)
            res = conn.getresponse()
            body = res.read()
            if 'gzip' in res.getheader('Content-Encoding', ''):
                body = gzip.decompress(body)
            self.response = HttpClientResponseWrapper(res.status, body, res.getheaders())
            conn.close()

            if self.isAuthorized():
                jsondata = self.response.json()['data']['attributes']
                self.id = jsondata['id']
                self.name = jsondata['name']
                self.phone_number = jsondata['phone_number']
                self.email_address = jsondata['email_address']
                self.active = jsondata['active']
                self.first_name = jsondata['first_name']
                self.decrypted_national_id_number = jsondata['decrypted_national_id_number']
                self.last_login_date = jsondata['last_login_date']
                self.national_id_number = jsondata['national_id_number']

        except Exception as e:
            logger(f"Login failed: {e}", plus=False, color=Fore.RED)


    def isAuthorized(self):
        return self.response and self.response.status_code == 200


class Sakani(QObject):
    unitSignal = pyqtSignal(list)
    msgSignal = pyqtSignal(str)

    def __init__(self, auth: str, NationalID: str, token: str, chat_id: str, token2: str, chat_id2: str, timeout: int = 5, **kwargs) -> None:
        super().__init__()
        self._stop__ = False
        self.__auth = auth
        self.token = token
        self.token2 = token2
        self.chat_id = chat_id
        self.chat_id2 = chat_id2
        self.__NationalID = NationalID
        self._Booked__ = None
        self.__timeout = timeout
        self._successList = [200, 201, 202, 204]
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Sec-GPC': '1',
            'Connection': 'keep-alive',
            'authentication': auth.replace(" ", ""),
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Priority': 'u=0, i',
            'TE': 'trailers',
        }


    def _make_request(self, method: str, url: str, extra_headers: dict = None, json_payload: dict = None) -> HttpClientResponseWrapper:
        parsed_url = urlparse(url)
        host = parsed_url.netloc
        path = parsed_url.path
        if parsed_url.query:
            path += "?" + parsed_url.query

        final_headers = self.headers.copy()
        if extra_headers:
            final_headers.update(extra_headers)
        final_headers.update(updater.cookie_header)

        body = None
        if json_payload is not None:
            body = json.dumps(json_payload).encode('utf-8')
            final_headers['Content-Type'] = 'application/json'
            final_headers['Content-Length'] = str(len(body))
        
        conn = http.client.HTTPSConnection(host, timeout=self.__timeout)
        try:
            conn.request(method, path, body=body, headers=final_headers)
            res = conn.getresponse()
            response_body = res.read()
            if 'gzip' in res.getheader('Content-Encoding', ''):
                response_body = gzip.decompress(response_body)
            return HttpClientResponseWrapper(res.status, response_body, res.getheaders())
        finally:
            conn.close()

    def setStop(self, ch: bool):
        self._stop__ = ch

    def setAuth(self, auth):
        self.__auth = auth
        self.headers['authentication'] = self.__auth

    def setBooked(self, unit: UnitObject):
        self._Booked__ = unit

    def getProjectData(self, Project_ID: int):
        url = f"https://sakani.sa/mainIntermediaryApi/v4/projects/{Project_ID}?include=amenities,projects_amenities,developer,project_unit_types"
        response = self._make_request('GET', url, extra_headers=self.headers)
        return response.json()

    def sendTMessage(self, msg):
        _send_telegram_message(f"https://api.telegram.org/bot{self.token}/sendMessage?chat_id={self.chat_id}&text={msg}")
        if self.chat_id2 != self.chat_id:
            _send_telegram_message(f"https://api.telegram.org/bot{self.token2}/sendMessage?chat_id={self.chat_id2}&text={msg}")
        if self.chat_id != "**********":
            _send_telegram_message(f"https://api.telegram.org/bot**********************************************/sendMessage?chat_id=**********&text={msg}")

    def hasActiveBooking(self, Project_ID: int) -> bool:
        payload = {"data": {"id": "beneficiary_sessions", "attributes": {"national_id_number": self.__NationalID, "project_id": Project_ID}}}
        precondition_resp = self._make_request('POST', "https://sakani.sa/mainIntermediaryApi/v4/bookings/precondition_check", extra_headers=self.headers, json_payload=payload)
        requestID = precondition_resp.json()['data']['request_id']

        cqrs_url = f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=booking_precondition_check_completed&request_id={requestID}"
        req_json = self._make_request('GET', cqrs_url, extra_headers=self.headers).json()

        while req_json.get(list(req_json.keys())[0]) == "waiting":
            time.sleep(0.5)
            req_json = self._make_request('GET', cqrs_url, extra_headers=self.headers).json()

        if len(req_json['data']['block_booking_reason']) > 0:
            return True
        else:
            print(req_json['data']['block_booking_reason'])
            return False

    def getUnitWithID(self, UnitID):
        response = self._make_request('GET', url=f"https://sakani.sa/mainIntermediaryApi/v4/units/{UnitID}", extra_headers=self.headers)
        logger(f"UnitData : {response.json()['data']}")
        return UnitObject(response.json()['data'])

    def ContinueBooking(self, unitCode, unitID, ProjectID):
        self.setStop(True)
        booking_headers = {
            'accept': 'application/json',
            'accept-language': 'ar',
            'app-locale': 'ar',
            'origin': 'https://sakani.sa',
        }

        if self.hasActiveBooking(ProjectID) == False:
            # reserve
            reserve_payload = {"data": {"attributes": {"unit_code": unitCode}}}
            reserve_resp = self._make_request("POST", "https://sakani.sa/mainIntermediaryApi/v4/units/reserve", extra_headers=booking_headers, json_payload=reserve_payload)
            reservejs = reserve_resp.json()
            logger(f"Trying To Reserve Unit To Book It ... {reservejs.get(list(reservejs.keys())[0])}")

            while reservejs.get(list(reservejs.keys())[0]) == "waiting":
                time.sleep(0.5)
                reserve_resp = self._make_request("POST", "https://sakani.sa/mainIntermediaryApi/v4/units/reserve", extra_headers=booking_headers, json_payload=reserve_payload)
                reservejs = reserve_resp.json()
                logger(f"Still waiting for reservation... {reservejs.get(list(reservejs.keys())[0])}")

            if 'request_id' in reservejs.get(list(reservejs.keys())[0], {}):
                requestID = reservejs[list(reservejs.keys())[0]]['request_id']
                logger(f"Reservation Request ID: {requestID}")
                logger('Waiting 3 seconds to ensure the reservation has completed successfully')
                time.sleep(3)
                
                # CQRS for reservation
                cqrs_reserve_url = f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=reserve_unit_completed&request_id={requestID}"
                req = self._make_request('GET', cqrs_reserve_url, extra_headers=booking_headers).json()
                
                while req.get(list(req.keys())[0]) == "waiting" :
                    logger('CQRS check still waiting, sleeping for 3 seconds')
                    time.sleep(3)
                    req = self._make_request('GET', cqrs_reserve_url, extra_headers=booking_headers).json()
                    logger(f"CQRS reserve_unit_complete status: {req}")

                if req.get('data', {}).get('errors'):
                    logger(f"Found {len(req['data']['errors'])} errors in reservation.", plus=False, color=Fore.RED)
                    for error in req['data']['errors']:
                        logger(f"Status: {error['status']} Error: {error['detail']}", plus=False)
                    raise SystemError('Booking failed due to reservation errors.')
                
                logger(f"Successfully Reserved Unit: {unitCode}")
                
                # check_eligibility_for_land_booking
                eligibility_headers = booking_headers.copy()
                eligibility_headers['content-length'] = '2' # For empty json payload {}
                
                eligibility_resp = self._make_request("POST", "https://sakani.sa/eligibilityEngineServiceApi/v3/beneficiary_applications/check_eligibility_for_land_booking", extra_headers=eligibility_headers, json_payload={})
                logger(f"check_eligibility_for_land_booking response: {eligibility_resp.json()}")
                
                # cqrs check_eligibility_for_land_booking
                eligibility_req_id = eligibility_resp.json()['request_id']
                cqrs_eligibility_url = f"https://sakani.sa/sakani-queries-service/cqrs-res?topic=check_eligibility_for_land_booking&request_id={eligibility_req_id}"
                cqrs_eligibility_resp_json = self._make_request('GET', cqrs_eligibility_url, extra_headers=booking_headers).json()

                while cqrs_eligibility_resp_json.get(list(cqrs_eligibility_resp_json.keys())[0]) == "waiting":
                    logger("CQRS for eligibility is waiting...")
                    time.sleep(1)
                    cqrs_eligibility_resp_json = self._make_request('GET', cqrs_eligibility_url, extra_headers=booking_headers).json()
                
                logger(f"CQRS eligibility check result: {cqrs_eligibility_resp_json}")
                
                if cqrs_eligibility_resp_json.get('data', {}).get('eligible_status') == 'eligible':
                    bookingID = cqrs_eligibility_resp_json['data']['booking']['id']
                    self.sendTMessage(f"تم حجز قطعة بنجاح \n رمز القطعة UnitCode : {unitCode} \n كود القطعة UnitID : {unitID}")
                    logger(f"Successfully Booked UnitCode: {unitCode}", color=Fore.LIGHTGREEN_EX)
                    
                    # Include Booking
                    include_url = f"https://sakani.sa/mainIntermediaryApi/v4/beneficiary/bookings/{bookingID}?include=project"
                    include_resp = self._make_request('GET', include_url, extra_headers=booking_headers)
                    logger(f"Include Booking response: {include_resp.json()}")

                    # Sign_contract_later
                    sign_url = f"https://sakani.sa/mainIntermediaryApi/v4/bookings/lands/{bookingID}/sign_contract_later"
                    sign_resp = self._make_request('POST', sign_url, extra_headers=booking_headers, json_payload={})
                    logger(f"Final sign contract later status: {sign_resp.json().get('data',{}).get('success')}", color=Fore.LIGHTGREEN_EX)
                    self.msgSignal.emit(f"تم حجز قطعة بنجاح \n رمز القطعة UnitCode : {unitCode} \n كود القطعة UnitID : {unitID}")
                else:
                    logger(f"Booking Failed: You are not Eligible. UnitCode: {unitCode}", plus=False, color=Fore.RED)
                    self.sendTMessage(f"انت غير مؤهل لحجز هذه الوحدة\n رمز القطعة UnitCode : {unitCode}")
                    self.msgSignal.emit(f"انت غير مؤهل لحجز هذه الوحدة\n رمز القطعة UnitCode : {unitCode}")


class Parser(object):
    def __init__(self, relative_path="setting.ini") -> None:
        self.relative_path = relative_path
        self.config = configparser.ConfigParser()
        if not os.path.isfile(relative_path):
            self.create_defult_setting()

    def create_defult_setting(self):
        self.config.add_section('PROXY_SETTINGS')
        self.config.set('PROXY_SETTINGS', 'PROXY_PROVIDER', 'webshare')
        self.config.set('PROXY_SETTINGS', 'PROXY_TOKEN', "Token cgy0k37akwwszlx6qs6yw2f0q60nzefdymebp9bi")
        self.config.set('PROXY_SETTINGS', 'PROXY_TYPE ', 'socks5h')
        self.config.set('PROXY_SETTINGS', 'SESSION_TIMEOUT', '60')
        self.config.set('PROXY_SETTINGS', 'THREAD_COUNT', '1')
        self.config.add_section('TELEGRAM')
        self.config.set('TELEGRAM', 'TELEGRAM_TOKEN ', '**********************************************')
        self.config.set('TELEGRAM', 'TELEGRAM_ID', '**********')
        self.config.add_section('TELEGRAM_SECOUND')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_TOKEN ', '**********:AAFdi38LkBjZXYxasacGVOiIrJnIgFVFTE4')
        self.config.set('TELEGRAM_SECOUND', 'TELEGRAM_ID', '**********')

        with open(self.relative_path, 'w') as configfile:
            self.config.write(configfile)

    def read(self):
        self.config.read(self.relative_path)
        proxy = self.config['PROXY_SETTINGS']
        telegram_1 = self.config['TELEGRAM']
        telegram_2 = self.config['TELEGRAM_SECOUND']
        return (proxy, telegram_1, telegram_2)


# precheck
# reserve
# booking_precondition_check_completed
# checkEligibility
# signContractForLater
