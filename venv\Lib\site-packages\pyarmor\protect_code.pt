def protect_pytransform():

    {relative}import pytransform{suffix} as pytransform

    def assert_builtin(func):
        type = ''.__class__.__class__
        builtin_function = type(''.join)
        if type(func) is not builtin_function:
            raise RuntimeError('%s() is not a builtin' % func.__name__)

    def check_obfuscated_script():
        CO_SIZES = 55, 52, 49, 46, 42, 40, 38, 36
        CO_NAMES = set(['pytransform{suffix}', 'pyarmor_runtime',
                        '__pyarmor{suffix}__', '__name__', '__file__'])
        co = pytransform.sys._getframe(3).f_code
        if not ((set(co.co_names) <= CO_NAMES)
                and (len(co.co_code) in CO_SIZES)):
            raise RuntimeError('unexpected obfuscated script')

    def check_mod_pytransform():
        def _check_co_key(co, v):
            return (len(co.co_names), len(co.co_consts), len(co.co_code)) == v
        for k, (v1, v2, v3) in {keylist}:
            co = getattr(pytransform, k).{code}
            if not _check_co_key(co, v1):
                raise RuntimeError('unexpected pytransform.py')
            if v2:
                if not _check_co_key(co.co_consts[1], v2):
                    raise RuntimeError('unexpected pytransform.py')
            if v3:
                if not _check_co_key(co.{closure}[0].cell_contents.{code}, v3):
                    raise RuntimeError('unexpected pytransform.py')

    def check_lib_pytransform():
        platname = pytransform.sys.platform
        if platname.startswith('darwin'):
            return
        libname = '_pytransform{suffix}.dylib' if platname.startswith('darwin') else \
                  '_pytransform{suffix}.dll' if platname.startswith('win') else \
                  '_pytransform{suffix}.dll' if platname.startswith('cygwin') else \
                  '_pytransform{suffix}.so'
        if getattr(pytransform.sys, 'frozen', False):
            filename = pytransform.os.path.join(pytransform.sys._MEIPASS, libname)
        else:
            filename = pytransform.os.path.join({rpath}, {spath}, libname)

        with open(filename, 'rb') as f:
            buf = bytearray(f.read())
        value = sum(buf)
        if getattr(pytransform.sys, 'frozen', False) and sys.platform == 'darwin':
            if '{suffix}':
                value += 886 - sum(b'{suffix}') + 299
            else:
                value += 1217

        if value not in {checksum}:
            raise RuntimeError('unexpected %s' % filename)

    assert_builtin(sum)
    assert_builtin(open)
    assert_builtin(len)

    check_obfuscated_script()
    check_mod_pytransform()
    check_lib_pytransform()


protect_pytransform()
